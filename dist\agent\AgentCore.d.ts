import { AIProviderManager } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ContextBuilder } from '../commands/ContextBuilder';
import { FileEditor } from '../commands/FileEditor';
import { Logger } from '../utils/Logger';
export interface AgentAction {
    type: 'analyze' | 'edit' | 'create' | 'search' | 'execute' | 'plan' | 'reflect' | 'complete';
    target?: string;
    instruction: string;
    reasoning: string;
    confidence: number;
}
export interface AgentState {
    currentTask: string;
    subTasks: string[];
    completedActions: AgentAction[];
    context: string;
    workingMemory: Map<string, any>;
    iteration: number;
    maxIterations: number;
}
export declare class AgentCore {
    private aiManager;
    private codebaseAnalyzer;
    private contextBuilder;
    private fileEditor;
    private logger;
    private taskPlanner;
    private actionExecutor;
    private memoryManager;
    private state;
    constructor(aiManager: AIProviderManager, codebaseAnalyzer: CodebaseAnalyzer, contextBuilder: ContextBuilder, fileEditor: FileEditor, logger: Logger);
    private initializeState;
    startAgent(task: string, options?: any): Promise<void>;
    startAutoAgent(goal: string, options?: any): Promise<void>;
    private planTask;
    private executePlan;
    private autonomousExecution;
    private decideAction;
    private decideAutonomousAction;
    private executeAction;
    private reflect;
    private selfReflect;
    private analyzeCurrentState;
    private attemptRecovery;
    private parseActionResponse;
    private askForConfirmation;
    private askForRetry;
}
//# sourceMappingURL=AgentCore.d.ts.map