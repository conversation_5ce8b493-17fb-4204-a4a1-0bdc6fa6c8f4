import * as vscode from 'vscode';

export class StatusProvider implements vscode.TreeDataProvider<StatusItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<StatusItem | undefined | null | void> = new vscode.EventEmitter<StatusItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<StatusItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor() {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: StatusItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: StatusItem): Thenable<StatusItem[]> {
        if (!element) {
            return Promise.resolve(this.getStatusItems());
        }
        return Promise.resolve([]);
    }

    private getStatusItems(): StatusItem[] {
        const config = vscode.workspace.getConfiguration('goc-agent');
        const defaultProvider = config.get('defaultProvider') as string;
        
        const items: StatusItem[] = [];

        // Current provider
        items.push(new StatusItem(
            `Provider: ${defaultProvider}`,
            vscode.TreeItemCollapsibleState.None,
            {
                command: 'goc-agent.selectProvider',
                title: 'Change Provider'
            }
        ));

        // Current model
        const modelKey = `${defaultProvider}Model`;
        const currentModel = config.get(modelKey) as string;
        items.push(new StatusItem(
            `Model: ${currentModel || 'default'}`,
            vscode.TreeItemCollapsibleState.None,
            {
                command: 'goc-agent.selectModel',
                title: 'Change Model'
            }
        ));

        // Provider status
        items.push(new StatusItem(
            'Status: Ready',
            vscode.TreeItemCollapsibleState.None
        ));

        // Quick actions
        items.push(new StatusItem(
            '💬 Start Chat',
            vscode.TreeItemCollapsibleState.None,
            {
                command: 'goc-agent.chat',
                title: 'Start Chat'
            }
        ));

        items.push(new StatusItem(
            '🤖 Agent Mode',
            vscode.TreeItemCollapsibleState.None,
            {
                command: 'goc-agent.agent',
                title: 'Start Agent'
            }
        ));

        items.push(new StatusItem(
            '🚀 Auto Mode',
            vscode.TreeItemCollapsibleState.None,
            {
                command: 'goc-agent.auto',
                title: 'Start Auto Agent'
            }
        ));

        return items;
    }
}

class StatusItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly command?: vscode.Command
    ) {
        super(label, collapsibleState);
        this.tooltip = this.label;
    }
}
