"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskPlanner = void 0;
class TaskPlanner {
    constructor(aiManager, logger) {
        this.aiManager = aiManager;
        this.logger = logger;
    }
    async createPlan(task, context) {
        const messages = [
            {
                role: 'system',
                content: `You are an expert software architect and project planner. Break down the given task into actionable sub-tasks.

Consider:
- Code architecture and best practices
- Dependencies between tasks
- Potential risks and challenges
- Realistic time estimates

Context:
${context}

Respond with JSON:
{
  "mainTask": "task description",
  "subTasks": ["step 1", "step 2", ...],
  "estimatedComplexity": "low|medium|high",
  "estimatedTime": "time estimate",
  "dependencies": ["dependency 1", ...],
  "risks": ["risk 1", ...]
}`
            },
            {
                role: 'user',
                content: `Please create a detailed plan for: ${task}`
            }
        ];
        try {
            const response = await this.aiManager.chat(messages);
            const plan = this.parsePlanResponse(response.content);
            this.logger.debug(`📋 Plan created with ${plan.subTasks.length} sub-tasks`);
            return plan;
        }
        catch (error) {
            this.logger.error(`Failed to create plan: ${error}`);
            return this.createFallbackPlan(task);
        }
    }
    async refinePlan(plan, feedback) {
        const messages = [
            {
                role: 'system',
                content: 'Refine the existing plan based on the feedback provided.'
            },
            {
                role: 'user',
                content: `Current plan: ${JSON.stringify(plan)}\n\nFeedback: ${feedback}\n\nProvide refined plan:`
            }
        ];
        try {
            const response = await this.aiManager.chat(messages);
            return this.parsePlanResponse(response.content);
        }
        catch (error) {
            this.logger.error(`Failed to refine plan: ${error}`);
            return plan; // Return original plan if refinement fails
        }
    }
    async adaptPlan(plan, currentProgress, obstacles) {
        const messages = [
            {
                role: 'system',
                content: 'Adapt the plan based on current progress and obstacles encountered.'
            },
            {
                role: 'user',
                content: `
Original plan: ${JSON.stringify(plan)}
Completed tasks: ${currentProgress.join(', ')}
Obstacles encountered: ${obstacles.join(', ')}

Provide adapted plan:`
            }
        ];
        try {
            const response = await this.aiManager.chat(messages);
            return this.parsePlanResponse(response.content);
        }
        catch (error) {
            this.logger.error(`Failed to adapt plan: ${error}`);
            return plan;
        }
    }
    parsePlanResponse(response) {
        try {
            const cleaned = response.replace(/```json\n?|\n?```/g, '').trim();
            const parsed = JSON.parse(cleaned);
            return {
                mainTask: parsed.mainTask || 'Unknown task',
                subTasks: Array.isArray(parsed.subTasks) ? parsed.subTasks : [],
                estimatedComplexity: parsed.estimatedComplexity || 'medium',
                estimatedTime: parsed.estimatedTime || 'Unknown',
                dependencies: Array.isArray(parsed.dependencies) ? parsed.dependencies : [],
                risks: Array.isArray(parsed.risks) ? parsed.risks : []
            };
        }
        catch (error) {
            this.logger.error(`Failed to parse plan response: ${error}`);
            return this.createFallbackPlan('Unknown task');
        }
    }
    createFallbackPlan(task) {
        return {
            mainTask: task,
            subTasks: [
                'Analyze the current codebase',
                'Identify required changes',
                'Implement the changes',
                'Test the implementation',
                'Review and refine'
            ],
            estimatedComplexity: 'medium',
            estimatedTime: '1-2 hours',
            dependencies: [],
            risks: ['Unclear requirements', 'Potential breaking changes']
        };
    }
}
exports.TaskPlanner = TaskPlanner;
//# sourceMappingURL=TaskPlanner.js.map