import { AIProviderManager } from '../ai/AIProviderManager';
import { ContextBuilder } from './ContextBuilder';
import { Logger } from '../utils/Logger';
export declare class FileEditor {
    private logger;
    constructor(logger: Logger);
    editFile(filePath: string, instruction: string, aiManager: AIProviderManager, contextBuilder: ContextBuilder): Promise<void>;
    createFile(filePath: string, instruction: string, aiManager: AIProviderManager, contextBuilder: ContextBuilder): Promise<void>;
    private extractCodeFromResponse;
    private createDiff;
}
//# sourceMappingURL=FileEditor.d.ts.map