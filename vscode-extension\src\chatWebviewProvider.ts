import * as vscode from 'vscode';

export class ChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'goc-agent.chat';
    private _view?: vscode.WebviewView;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        webviewView.webview.onDidReceiveMessage(data => {
            switch (data.type) {
                case 'sendMessage':
                    this.handleMessage(data.message);
                    break;
                case 'selectProvider':
                    this.handleProviderSelection(data.provider);
                    break;
                case 'selectModel':
                    this.handleModelSelection(data.model);
                    break;
            }
        });
    }

    public show() {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private async handleMessage(message: string) {
        if (!this._view) return;

        // Add user message to chat
        this._view.webview.postMessage({
            type: 'addMessage',
            message: { role: 'user', content: message }
        });

        try {
            // Get AI response (this would integrate with your GOC agent)
            const response = await this.getAIResponse(message);
            
            // Add AI response to chat
            this._view.webview.postMessage({
                type: 'addMessage',
                message: { role: 'assistant', content: response }
            });
        } catch (error) {
            this._view.webview.postMessage({
                type: 'addMessage',
                message: { role: 'assistant', content: `Error: ${error}` }
            });
        }
    }

    private async handleProviderSelection(provider: string) {
        await vscode.workspace.getConfiguration('goc-agent').update('defaultProvider', provider, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage(`Switched to ${provider}`);
    }

    private async handleModelSelection(model: string) {
        const provider = vscode.workspace.getConfiguration('goc-agent').get('defaultProvider') as string;
        await vscode.workspace.getConfiguration('goc-agent').update(`${provider}Model`, model, vscode.ConfigurationTarget.Global);
        vscode.window.showInformationMessage(`Model changed to ${model}`);
    }

    private async getAIResponse(message: string): Promise<string> {
        // This would integrate with your GOC agent
        // For now, return a placeholder
        return `AI Response to: ${message}`;
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GOC Agent Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 10px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            background-color: var(--vscode-panel-background);
            border-radius: 4px;
        }
        
        .provider-select, .model-select {
            flex: 1;
            padding: 4px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: var(--vscode-panel-background);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 85%;
        }
        
        .message.user {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
        }
        
        .message.system {
            background-color: var(--vscode-textCodeBlock-background);
            font-style: italic;
            text-align: center;
            margin: 0 auto;
        }
        
        .input-container {
            display: flex;
            gap: 8px;
        }
        
        .message-input {
            flex: 1;
            padding: 8px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            outline: none;
        }
        
        .send-button {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            font-style: italic;
            color: var(--vscode-descriptionForeground);
            padding: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <select class="provider-select" id="providerSelect">
            <option value="ollama">Ollama</option>
            <option value="openai">OpenAI</option>
            <option value="groq">Groq</option>
            <option value="gemini">Gemini</option>
        </select>
        <select class="model-select" id="modelSelect">
            <option value="llama3.2">llama3.2</option>
        </select>
    </div>
    
    <div class="chat-container" id="chatContainer">
        <div class="message system">
            🤖 GOC Agent is ready! Ask me anything about your code.
        </div>
    </div>
    
    <div class="typing-indicator" id="typingIndicator">
        AI is thinking...
    </div>
    
    <div class="input-container">
        <input type="text" class="message-input" id="messageInput" placeholder="Ask about your code..." />
        <button class="send-button" id="sendButton">Send</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const providerSelect = document.getElementById('providerSelect');
        const modelSelect = document.getElementById('modelSelect');

        function addMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = \`message \${message.role}\`;
            messageDiv.textContent = message.content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Disable input while processing
            messageInput.disabled = true;
            sendButton.disabled = true;
            typingIndicator.style.display = 'block';

            // Send message to extension
            vscode.postMessage({
                type: 'sendMessage',
                message: message
            });

            messageInput.value = '';
        }

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);

        providerSelect.addEventListener('change', (e) => {
            vscode.postMessage({
                type: 'selectProvider',
                provider: e.target.value
            });
        });

        modelSelect.addEventListener('change', (e) => {
            vscode.postMessage({
                type: 'selectModel',
                model: e.target.value
            });
        });

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'addMessage':
                    addMessage(message.message);
                    // Re-enable input
                    messageInput.disabled = false;
                    sendButton.disabled = false;
                    typingIndicator.style.display = 'none';
                    messageInput.focus();
                    break;
            }
        });

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>`;
    }
}
