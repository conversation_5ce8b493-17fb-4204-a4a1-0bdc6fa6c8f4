import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { Logger } from '../utils/Logger';
export declare class ContextBuilder {
    private codebaseAnalyzer;
    private logger;
    private maxContextLength;
    constructor(codebaseAnalyzer: CodebaseAnalyzer, logger: Logger);
    buildContext(): Promise<string>;
    buildFileContext(filePath: string): Promise<string>;
    buildEditContext(filePath: string, instruction: string): Promise<string>;
    private selectImportantFiles;
    private findRelatedFiles;
    private getLanguageFromExtension;
    private formatBytes;
}
//# sourceMappingURL=ContextBuilder.d.ts.map