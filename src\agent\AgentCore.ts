import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ContextBuilder } from '../commands/ContextBuilder';
import { FileEditor } from '../commands/FileEditor';
import { Logger } from '../utils/Logger';
import { TaskPlanner } from './TaskPlanner';
import { ActionExecutor } from './ActionExecutor';
import { MemoryManager } from './MemoryManager';

export interface AgentAction {
  type: 'analyze' | 'edit' | 'create' | 'search' | 'execute' | 'plan' | 'reflect' | 'complete';
  target?: string;
  instruction: string;
  reasoning: string;
  confidence: number;
}

export interface AgentState {
  currentTask: string;
  subTasks: string[];
  completedActions: AgentAction[];
  context: string;
  workingMemory: Map<string, any>;
  iteration: number;
  maxIterations: number;
}

export class AgentCore {
  private aiManager: AIProviderManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private contextBuilder: ContextBuilder;
  private fileEditor: FileEditor;
  private logger: Logger;
  private taskPlanner: TaskPlanner;
  private actionExecutor: ActionExecutor;
  private memoryManager: MemoryManager;
  private state: AgentState;

  constructor(
    aiManager: AIProviderManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    contextBuilder: ContextBuilder,
    fileEditor: FileEditor,
    logger: Logger
  ) {
    this.aiManager = aiManager;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.contextBuilder = contextBuilder;
    this.fileEditor = fileEditor;
    this.logger = logger;
    
    this.taskPlanner = new TaskPlanner(aiManager, logger);
    this.actionExecutor = new ActionExecutor(codebaseAnalyzer, fileEditor, logger);
    this.memoryManager = new MemoryManager(logger);
    
    this.state = this.initializeState();
  }

  private initializeState(): AgentState {
    return {
      currentTask: '',
      subTasks: [],
      completedActions: [],
      context: '',
      workingMemory: new Map(),
      iteration: 0,
      maxIterations: 10
    };
  }

  public async startAgent(task: string, options: any = {}): Promise<void> {
    this.logger.info(`🤖 Starting Agent Mode`);
    this.logger.info(`📋 Task: ${task}`);
    
    this.state.currentTask = task;
    this.state.maxIterations = parseInt(options.maxIterations) || 10;
    this.state.context = await this.contextBuilder.buildContext();
    
    // Plan the task
    await this.planTask(task);
    
    // Execute the plan
    await this.executePlan(options.auto || false);
  }

  public async startAutoAgent(goal: string, options: any = {}): Promise<void> {
    this.logger.info(`🚀 Starting Auto Agent Mode`);
    this.logger.info(`🎯 Goal: ${goal}`);
    
    this.state.currentTask = goal;
    this.state.maxIterations = parseInt(options.maxIterations) || 20;
    this.state.context = await this.contextBuilder.buildContext();
    
    // Auto mode - fully autonomous
    await this.autonomousExecution(goal);
  }

  private async planTask(task: string): Promise<void> {
    this.logger.loading('🧠 Planning task...');
    
    const plan = await this.taskPlanner.createPlan(task, this.state.context);
    this.state.subTasks = plan.subTasks;
    
    this.logger.info(`📝 Created plan with ${plan.subTasks.length} sub-tasks:`);
    plan.subTasks.forEach((subTask, index) => {
      this.logger.info(`  ${index + 1}. ${subTask}`);
    });
  }

  private async executePlan(autoMode: boolean = false): Promise<void> {
    for (let i = 0; i < this.state.subTasks.length && this.state.iteration < this.state.maxIterations; i++) {
      const subTask = this.state.subTasks[i];
      this.state.iteration++;
      
      this.logger.info(`\n🔄 Iteration ${this.state.iteration}: ${subTask}`);
      
      try {
        // Decide on action
        const action = await this.decideAction(subTask);
        
        if (!autoMode) {
          // Ask for confirmation in agent mode
          const proceed = await this.askForConfirmation(action);
          if (!proceed) {
            this.logger.info('⏸️  Task paused by user');
            break;
          }
        }
        
        // Execute action
        await this.executeAction(action);
        
        // Reflect on results
        await this.reflect(action);
        
        // Update memory
        this.memoryManager.addExperience(subTask, action, 'success');
        
      } catch (error) {
        this.logger.error(`❌ Error executing sub-task: ${error}`);
        this.memoryManager.addExperience(subTask, null, 'failure', String(error));
        
        if (!autoMode) {
          const retry = await this.askForRetry();
          if (!retry) break;
        }
      }
    }
    
    this.logger.success('✅ Agent execution completed');
  }

  private async autonomousExecution(goal: string): Promise<void> {
    while (this.state.iteration < this.state.maxIterations) {
      this.state.iteration++;
      
      this.logger.info(`\n🔄 Auto Iteration ${this.state.iteration}`);
      
      try {
        // Analyze current state
        const currentState = await this.analyzeCurrentState();
        
        // Decide next action autonomously
        const action = await this.decideAutonomousAction(goal, currentState);
        
        if (action.type === 'complete') {
          this.logger.success('🎉 Goal achieved!');
          break;
        }
        
        // Execute action
        await this.executeAction(action);
        
        // Self-reflect and adapt
        await this.selfReflect(action);
        
        // Update memory
        this.memoryManager.addExperience(goal, action, 'success');
        
      } catch (error) {
        this.logger.error(`❌ Auto execution error: ${error}`);
        
        // Try to recover autonomously
        const recovery = await this.attemptRecovery(String(error));
        if (!recovery) {
          this.logger.error('💥 Unable to recover, stopping auto mode');
          break;
        }
      }
    }
  }

  private async decideAction(task: string): Promise<AgentAction> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are an intelligent coding agent. Analyze the task and decide on the best action.

Available actions:
- analyze: Analyze code or codebase
- edit: Edit an existing file
- create: Create a new file
- search: Search through codebase
- execute: Execute a command
- plan: Create a detailed plan
- reflect: Reflect on progress

Context:
${this.state.context}

Previous actions:
${this.state.completedActions.map(a => `${a.type}: ${a.instruction}`).join('\n')}

Respond with JSON:
{
  "type": "action_type",
  "target": "file_path_or_query",
  "instruction": "detailed instruction",
  "reasoning": "why this action",
  "confidence": 0.8
}`
      },
      {
        role: 'user',
        content: `Task: ${task}`
      }
    ];

    const response = await this.aiManager.chat(messages);
    return this.parseActionResponse(response.content);
  }

  private async decideAutonomousAction(goal: string, currentState: string): Promise<AgentAction> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are a fully autonomous coding agent. Analyze the goal and current state, then decide the next action.

You have complete autonomy to:
- Analyze and understand the codebase
- Create new files and features
- Edit existing code
- Execute commands
- Make architectural decisions

Goal: ${goal}
Current State: ${currentState}

Memory of past experiences:
${this.memoryManager.getRelevantExperiences(goal)}

Respond with JSON for the next action, or {"type": "complete"} if goal is achieved.`
      }
    ];

    const response = await this.aiManager.chat(messages);
    return this.parseActionResponse(response.content);
  }

  private async executeAction(action: AgentAction): Promise<void> {
    this.logger.info(`🎬 Executing: ${action.type} - ${action.instruction}`);
    
    await this.actionExecutor.execute(action, this.aiManager, this.contextBuilder);
    this.state.completedActions.push(action);
  }

  private async reflect(action: AgentAction): Promise<void> {
    // Simple reflection for now
    this.logger.debug(`💭 Reflecting on action: ${action.type}`);
  }

  private async selfReflect(action: AgentAction): Promise<void> {
    // Advanced self-reflection for auto mode
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'Reflect on the action taken. What was learned? What could be improved?'
      },
      {
        role: 'user',
        content: `Action taken: ${JSON.stringify(action)}`
      }
    ];

    const response = await this.aiManager.chat(messages);
    this.memoryManager.addReflection(action, response.content);
  }

  private async analyzeCurrentState(): Promise<string> {
    const context = await this.codebaseAnalyzer.scanCodebase();
    return `Files: ${context.totalFiles}, Languages: ${context.languages.join(', ')}`;
  }

  private async attemptRecovery(error: string): Promise<boolean> {
    this.logger.info('🔧 Attempting autonomous recovery...');
    // Implement recovery logic
    return false;
  }

  private parseActionResponse(response: string): AgentAction {
    try {
      const cleaned = response.replace(/```json\n?|\n?```/g, '').trim();
      return JSON.parse(cleaned);
    } catch (error) {
      // Fallback action
      return {
        type: 'analyze',
        instruction: 'Analyze the current situation',
        reasoning: 'Failed to parse AI response',
        confidence: 0.5
      };
    }
  }

  private async askForConfirmation(action: AgentAction): Promise<boolean> {
    // This would be implemented with inquirer in the command handler
    return true;
  }

  private async askForRetry(): Promise<boolean> {
    // This would be implemented with inquirer in the command handler
    return false;
  }
}
