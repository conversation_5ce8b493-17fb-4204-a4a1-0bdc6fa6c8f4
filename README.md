# GOC Agent

A powerful coding agent with support for multiple AI providers including Ollama, OpenAI, Groq, and Gemini.

## Features

- **Multiple AI Providers**: Support for Ollama (local), OpenAI, Groq, and Gemini
- **Codebase Analysis**: Intelligent scanning and context building
- **Interactive Chat**: Chat with AI about your codebase
- **File Editing**: AI-assisted file editing with diff preview
- **Code Explanation**: Get explanations for specific code sections
- **Search**: Search through your codebase with text or regex
- **Configuration Management**: Easy setup and provider configuration

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Build the project:
   ```bash
   npm run build
   ```
4. Link globally (optional):
   ```bash
   npm link
   ```

## Quick Start

### 1. Configure AI Providers

First, configure your AI providers:

```bash
npm run dev config
```

This will guide you through setting up:
- **Ollama**: Local AI (no API key needed, just install Ollama)
- **OpenAI**: Requires API key
- **Groq**: Requires API key  
- **Gemini**: Requires API key

### 2. Start Chatting

Begin an interactive chat session:

```bash
npm run dev chat
```

Or specify a provider:

```bash
npm run dev chat --provider ollama
npm run dev chat --provider openai --model gpt-4
```

### 3. Analyze Your Codebase

Get an overview of your project:

```bash
npm run dev analyze
```

### 4. Edit Files with AI

Edit a file with AI assistance:

```bash
npm run dev edit src/example.ts --instruction "Add error handling to this function"
```

### 5. Explain Code

Get explanations for code:

```bash
npm run dev explain src/example.ts
npm run dev explain src/example.ts --lines 10-20
```

### 6. Search Code

Search through your codebase:

```bash
npm run dev search "function name"
npm run dev search "import.*react" --type regex
```

## Commands

- `goc config` - Configure AI providers and settings
- `goc chat` - Start interactive chat session
- `goc analyze` - Analyze current codebase
- `goc edit <file>` - Edit file with AI assistance
- `goc explain <file>` - Explain code in file
- `goc search <query>` - Search through codebase
- `goc exec <command>` - Execute shell command (coming soon)

## AI Providers

### Ollama (Recommended for Local Use)

1. Install Ollama from https://ollama.ai
2. Pull a model: `ollama pull llama3.2`
3. The agent will automatically detect Ollama running on localhost:11434

### OpenAI

1. Get API key from https://platform.openai.com
2. Configure via `goc config`
3. Supports GPT-4, GPT-3.5-turbo, etc.

### Groq

1. Get API key from https://console.groq.com
2. Configure via `goc config`
3. Fast inference with Llama models

### Gemini

1. Get API key from Google AI Studio
2. Configure via `goc config`
3. Supports Gemini Pro models

## Configuration

Configuration is stored in `~/.goc-agent/config.yaml`. You can edit this file directly or use the `goc config` command.

Example configuration:
```yaml
defaultProvider: ollama
providers:
  ollama:
    name: Ollama
    baseUrl: http://localhost:11434
    defaultModel: llama3.2
    enabled: true
  openai:
    name: OpenAI
    baseUrl: https://api.openai.com/v1
    apiKey: your-api-key-here
    defaultModel: gpt-4
    enabled: true
```

## Development

Run in development mode:
```bash
npm run dev <command>
```

Watch for changes:
```bash
npm run watch
```

## Architecture

The agent is built with a modular architecture:

- **CLI Layer**: Command-line interface using Commander.js
- **AI Providers**: Pluggable AI service integrations
- **Codebase Analyzer**: File scanning and context building
- **Command Handlers**: Business logic for each command
- **Context Builder**: Smart context preparation for AI
- **File Editor**: AI-assisted file editing with diff preview

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
