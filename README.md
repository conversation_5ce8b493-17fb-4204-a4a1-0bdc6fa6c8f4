# GOC Agent

A powerful coding agent with support for multiple AI providers including Ollama, OpenAI, Groq, and Gemini.

## Features

### 🤖 **Intelligent Agent Modes**
- **Chat Mode**: Interactive conversations about your codebase
- **Agent Mode**: Autonomous task execution with user confirmation
- **Auto Mode**: Fully autonomous coding with minimal supervision
- **Memory & Learning**: Learns from interactions and improves over time

### 🔧 **AI Provider Support**
- **Ollama**: Local AI models (llama3.2, codellama, etc.)
- **OpenAI**: GPT-4, GPT-3.5-turbo, and other models
- **Groq**: Fast inference with Llama models
- **Gemini**: Google's AI models
- **Model Selection**: Easy switching between models and providers

### 📊 **Codebase Intelligence**
- **Smart Analysis**: Deep codebase understanding and context building
- **File Operations**: AI-assisted editing with diff preview and backup
- **Code Explanation**: Detailed explanations for any code section
- **Search & Discovery**: Semantic and text-based code search
- **Architecture Analysis**: Understanding project structure and patterns

### 🧠 **Training & Learning**
- **Experience Memory**: Learns from successful and failed attempts
- **Training Data Generation**: Automatically creates training examples
- **Model Optimization**: Improves performance based on usage patterns
- **Export/Import**: Share and transfer learned knowledge
- **Performance Analytics**: Track improvement over time

### 🎯 **VS Code Integration**
- **Native Extension**: Full VS Code integration with webview chat
- **Context Menus**: Right-click to explain or edit code
- **Keyboard Shortcuts**: Quick access to common functions
- **Status Panel**: Monitor agent status and switch models
- **Diff Views**: Visual comparison of AI-generated changes

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Build the project:
   ```bash
   npm run build
   ```
4. Link globally (optional):
   ```bash
   npm link
   ```

## Quick Start

### 1. Configure AI Providers

First, configure your AI providers:

```bash
npm run dev config
```

This will guide you through setting up:
- **Ollama**: Local AI (no API key needed, just install Ollama)
- **OpenAI**: Requires API key
- **Groq**: Requires API key  
- **Gemini**: Requires API key

### 2. Start Chatting

Begin an interactive chat session:

```bash
npm run dev chat
```

Or specify a provider:

```bash
npm run dev chat --provider ollama
npm run dev chat --provider openai --model gpt-4
```

### 3. Analyze Your Codebase

Get an overview of your project:

```bash
npm run dev analyze
```

### 4. Edit Files with AI

Edit a file with AI assistance:

```bash
npm run dev edit src/example.ts --instruction "Add error handling to this function"
```

### 5. Explain Code

Get explanations for code:

```bash
npm run dev explain src/example.ts
npm run dev explain src/example.ts --lines 10-20
```

### 6. Search Code

Search through your codebase:

```bash
npm run dev search "function name"
npm run dev search "import.*react" --type regex
```

## Commands

### 🤖 **Agent Commands**
- `goc` - Show welcome screen and quick start guide
- `goc chat` - Start interactive chat session
- `goc agent` - Start autonomous agent mode
- `goc auto` - Start fully autonomous coding mode

### 📊 **Analysis & Editing**
- `goc analyze` - Analyze current codebase
- `goc edit <file>` - Edit file with AI assistance
- `goc explain <file>` - Explain code in file
- `goc search <query>` - Search through codebase

### ⚙️ **Configuration & Models**
- `goc config` - Configure AI providers and settings
- `goc models` - Manage AI models (list, pull, remove)
- `goc models --pull <model>` - Pull/download a model (Ollama)

### 🧠 **Training & Learning**
- `goc train` - Interactive training menu
- `goc train --generate` - Generate training data from codebase
- `goc train --stats` - Show training statistics
- `goc train --export <path>` - Export training dataset
- `goc train --import <path>` - Import training dataset
- `goc train --optimize` - Optimize model based on training data

### 🔧 **Utilities**
- `goc exec <command>` - Execute shell command with AI assistance

## AI Providers

### Ollama (Recommended for Local Use)

1. Install Ollama from https://ollama.ai
2. Pull a model: `ollama pull llama3.2`
3. The agent will automatically detect Ollama running on localhost:11434

### OpenAI

1. Get API key from https://platform.openai.com
2. Configure via `goc config`
3. Supports GPT-4, GPT-3.5-turbo, etc.

### Groq

1. Get API key from https://console.groq.com
2. Configure via `goc config`
3. Fast inference with Llama models

### Gemini

1. Get API key from Google AI Studio
2. Configure via `goc config`
3. Supports Gemini Pro models

## Configuration

Configuration is stored in `~/.goc-agent/config.yaml`. You can edit this file directly or use the `goc config` command.

Example configuration:
```yaml
defaultProvider: ollama
providers:
  ollama:
    name: Ollama
    baseUrl: http://localhost:11434
    defaultModel: llama3.2
    enabled: true
  openai:
    name: OpenAI
    baseUrl: https://api.openai.com/v1
    apiKey: your-api-key-here
    defaultModel: gpt-4
    enabled: true
```

## Development

Run in development mode:
```bash
npm run dev <command>
```

Watch for changes:
```bash
npm run watch
```

## Architecture

The agent is built with a modular architecture:

- **CLI Layer**: Command-line interface using Commander.js
- **AI Providers**: Pluggable AI service integrations
- **Codebase Analyzer**: File scanning and context building
- **Command Handlers**: Business logic for each command
- **Context Builder**: Smart context preparation for AI
- **File Editor**: AI-assisted file editing with diff preview

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
