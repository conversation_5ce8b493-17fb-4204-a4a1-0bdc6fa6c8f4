import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { FileEditor } from '../commands/FileEditor';
import { ContextBuilder } from '../commands/ContextBuilder';
import { AIProviderManager } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
import { AgentAction } from './AgentCore';
export declare class ActionExecutor {
    private codebaseAnalyzer;
    private fileEditor;
    private logger;
    constructor(codebaseAnalyzer: CodebaseAnalyzer, fileEditor: FileEditor, logger: Logger);
    execute(action: AgentAction, aiManager: AIProviderManager, contextBuilder: ContextBuilder): Promise<any>;
    private executeAnalyze;
    private executeEdit;
    private executeCreate;
    private executeSearch;
    private executeCommand;
    private executePlan;
    private executeReflect;
}
//# sourceMappingURL=ActionExecutor.d.ts.map