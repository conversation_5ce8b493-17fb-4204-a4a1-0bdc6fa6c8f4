import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import Table from 'cli-table3';
import { ConfigManager } from '../config/ConfigManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
import { ContextBuilder } from './ContextBuilder';
import { FileEditor } from './FileEditor';
import { AgentCore } from '../agent/AgentCore';
import { MemoryManager } from '../agent/MemoryManager';
import { TrainingManager } from '../training/TrainingManager';

export class CommandHandler {
  private config: ConfigManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private aiManager: AIProviderManager;
  private logger: Logger;
  private contextBuilder: ContextBuilder;
  private fileEditor: FileEditor;
  private agentCore: AgentCore;
  private memoryManager: MemoryManager;
  private trainingManager: TrainingManager;

  constructor(
    config: ConfigManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    aiManager: AIProviderManager,
    logger: Logger
  ) {
    this.config = config;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.aiManager = aiManager;
    this.logger = logger;
    this.contextBuilder = new ContextBuilder(codebaseAnalyzer, logger);
    this.fileEditor = new FileEditor(logger);
    this.memoryManager = new MemoryManager(logger);
    this.trainingManager = new TrainingManager(logger, this.memoryManager, codebaseAnalyzer);
    this.agentCore = new AgentCore(aiManager, codebaseAnalyzer, this.contextBuilder, this.fileEditor, logger);
  }

  public async handleConfig(): Promise<void> {
    const choices = [
      'View current configuration',
      'Configure AI providers',
      'Set default provider',
      'Configure codebase settings',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices
      }
    ]);

    switch (action) {
      case 'View current configuration':
        await this.showCurrentConfig();
        break;
      case 'Configure AI providers':
        await this.configureProviders();
        break;
      case 'Set default provider':
        await this.setDefaultProvider();
        break;
      case 'Configure codebase settings':
        await this.configureCodebase();
        break;
    }
  }

  public async handleChat(options: any): Promise<void> {
    const providerName = options.provider;
    const model = options.model;

    // Check if provider is available
    if (providerName) {
      const provider = this.aiManager.getProvider(providerName);
      if (!provider) {
        this.logger.error(`Provider '${providerName}' not found or not configured`);
        return;
      }
    }

    this.logger.info('Starting interactive chat session...');
    this.logger.info('Type "exit" to quit, "help" for commands');

    const messages: ChatMessage[] = [];
    
    // Add system context
    const context = await this.contextBuilder.buildContext();
    if (context) {
      messages.push({
        role: 'system',
        content: context
      });
    }

    while (true) {
      const { input } = await inquirer.prompt([
        {
          type: 'input',
          name: 'input',
          message: chalk.green('You:'),
        }
      ]);

      if (input.toLowerCase() === 'exit') {
        break;
      }

      if (input.toLowerCase() === 'help') {
        this.showChatHelp();
        continue;
      }

      if (input.trim() === '') {
        continue;
      }

      messages.push({
        role: 'user',
        content: input
      });

      try {
        this.logger.loading('Thinking...');
        const response = await this.aiManager.chat(messages, providerName, model);
        
        console.log(chalk.blue('Assistant:'), response.content);
        
        if (response.usage) {
          this.logger.debug(`Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`);
        }

        messages.push({
          role: 'assistant',
          content: response.content
        });

      } catch (error) {
        this.logger.error(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    this.logger.info('Chat session ended');
  }

  public async handleAnalyze(options: any): Promise<void> {
    this.logger.loading('Analyzing codebase...');
    
    try {
      const context = await this.codebaseAnalyzer.scanCodebase();
      
      console.log(boxen(
        `Codebase Analysis\n\n` +
        `Files: ${context.totalFiles}\n` +
        `Total Size: ${this.formatBytes(context.totalSize)}\n` +
        `Languages: ${context.languages.join(', ')}\n`,
        { padding: 1, borderColor: 'blue' }
      ));

      if (options.files) {
        const files = await this.codebaseAnalyzer.searchFiles(options.files);
        console.log(`\nMatching files (${files.length}):`);
        files.forEach(file => {
          console.log(`  ${file.relativePath}`);
        });
      } else {
        console.log('\nProject Structure:');
        console.log(context.structure);
      }

    } catch (error) {
      this.logger.error(`Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleEdit(file: string, options: any): Promise<void> {
    if (!options.instruction) {
      const { instruction } = await inquirer.prompt([
        {
          type: 'input',
          name: 'instruction',
          message: 'What changes would you like to make?',
          validate: (input) => input.trim() !== '' || 'Please provide an instruction'
        }
      ]);
      options.instruction = instruction;
    }

    try {
      await this.fileEditor.editFile(file, options.instruction, this.aiManager, this.contextBuilder);
      this.logger.success(`File ${file} edited successfully`);
    } catch (error) {
      this.logger.error(`Edit failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleExplain(file: string, options: any): Promise<void> {
    try {
      const content = await this.codebaseAnalyzer.getFileContent(file);
      const context = await this.contextBuilder.buildFileContext(file);
      
      let prompt = `Please explain this code:\n\n${content}`;
      
      if (options.lines) {
        const [start, end] = options.lines.split('-').map(Number);
        const lines = content.split('\n');
        const selectedLines = lines.slice(start - 1, end).join('\n');
        prompt = `Please explain these lines (${start}-${end}) from ${file}:\n\n${selectedLines}`;
      }

      const messages: ChatMessage[] = [
        { role: 'system', content: context },
        { role: 'user', content: prompt }
      ];

      this.logger.loading('Analyzing code...');
      const response = await this.aiManager.chat(messages);
      
      console.log(boxen(response.content, { 
        padding: 1, 
        borderColor: 'green',
        title: `Code Explanation: ${file}`
      }));

    } catch (error) {
      this.logger.error(`Explanation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleSearch(query: string, options: any): Promise<void> {
    try {
      const searchType = options.type || 'text';
      const results = await this.codebaseAnalyzer.searchFiles(query, searchType);
      
      if (results.length === 0) {
        this.logger.info('No matches found');
        return;
      }

      console.log(`\nFound ${results.length} matches:`);
      results.forEach(file => {
        console.log(`  ${chalk.blue(file.relativePath)} (${file.size} bytes)`);
      });

    } catch (error) {
      this.logger.error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleExec(command: string): Promise<void> {
    this.logger.info(`Executing: ${command}`);
    // TODO: Implement shell command execution with AI assistance
    this.logger.warn('Command execution not yet implemented');
  }

  public async handleAgent(options: any): Promise<void> {
    const task = options.task || await this.promptForTask();

    this.logger.info(`🤖 Starting Agent Mode`);
    this.logger.info(`📋 Task: ${task}`);

    try {
      await this.agentCore.startAgent(task, options);
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleAutoAgent(options: any): Promise<void> {
    const goal = options.goal || await this.promptForGoal();

    this.logger.info(`🚀 Starting Auto Agent Mode`);
    this.logger.info(`🎯 Goal: ${goal}`);

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.yellow('⚠️  Auto mode will make autonomous changes to your codebase. Continue?'),
        default: false
      }
    ]);

    if (!confirm) {
      this.logger.info('Auto agent mode cancelled');
      return;
    }

    try {
      await this.agentCore.startAutoAgent(goal, options);
    } catch (error) {
      this.logger.error(`Auto agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleModels(options: any): Promise<void> {
    if (options.pull) {
      await this.pullModel(options.pull, options.provider);
    } else if (options.remove) {
      await this.removeModel(options.remove, options.provider);
    } else {
      await this.listModels(options.provider);
    }
  }

  private async showCurrentConfig(): Promise<void> {
    const config = this.config.getConfig();
    
    console.log(boxen(
      `Current Configuration\n\n` +
      `Default Provider: ${config.defaultProvider}\n` +
      `Config Path: ${this.config.getConfigPath()}\n`,
      { padding: 1, borderColor: 'cyan' }
    ));

    const table = new Table({
      head: ['Provider', 'Status', 'Model', 'URL'],
      colWidths: [12, 12, 20, 40]
    });

    for (const [name, provider] of Object.entries(config.providers)) {
      const status = provider.enabled ? 
        (provider.apiKey || name === 'ollama' ? chalk.green('✓ Ready') : chalk.yellow('⚠ No API Key')) :
        chalk.red('✗ Disabled');
      
      table.push([
        name,
        status,
        provider.defaultModel || 'N/A',
        provider.baseUrl || 'N/A'
      ]);
    }

    console.log(table.toString());
  }

  private async configureProviders(): Promise<void> {
    const config = this.config.getConfig();
    const providers = Object.keys(config.providers);

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Which provider would you like to configure?',
        choices: providers
      }
    ]);

    const providerConfig = config.providers[provider as keyof typeof config.providers];
    
    const { apiKey, enabled } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter API key for ${provider}:`,
        when: provider !== 'ollama',
        default: providerConfig.apiKey
      },
      {
        type: 'confirm',
        name: 'enabled',
        message: `Enable ${provider}?`,
        default: providerConfig.enabled
      }
    ]);

    if (apiKey) {
      this.config.setProviderApiKey(provider, apiKey);
    }
    this.config.enableProvider(provider, enabled);

    this.logger.success(`${provider} configuration updated`);
  }

  private async setDefaultProvider(): Promise<void> {
    const availableProviders = await this.aiManager.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      this.logger.error('No providers are available. Please configure at least one provider first.');
      return;
    }

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select default provider:',
        choices: availableProviders
      }
    ]);

    this.config.updateConfig({ defaultProvider: provider });
    this.logger.success(`Default provider set to ${provider}`);
  }

  private async configureCodebase(): Promise<void> {
    this.logger.info('Codebase configuration not yet implemented');
  }

  private showChatHelp(): void {
    console.log(boxen(
      'Chat Commands:\n\n' +
      'exit - End the chat session\n' +
      'help - Show this help message\n',
      { padding: 1, borderColor: 'yellow', title: 'Help' }
    ));
  }

  private async promptForTask(): Promise<string> {
    const { task } = await inquirer.prompt([
      {
        type: 'input',
        name: 'task',
        message: 'What task would you like the agent to perform?',
        validate: (input) => input.trim() !== '' || 'Please provide a task description'
      }
    ]);
    return task;
  }

  private async promptForGoal(): Promise<string> {
    const { goal } = await inquirer.prompt([
      {
        type: 'input',
        name: 'goal',
        message: 'What is your high-level goal for the auto agent?',
        validate: (input) => input.trim() !== '' || 'Please provide a goal description'
      }
    ]);
    return goal;
  }

  private async listModels(providerName?: string): Promise<void> {
    try {
      const modelsList = await this.aiManager.listModels(providerName);

      if (modelsList.length === 0) {
        this.logger.info('No models available');
        return;
      }

      console.log('\nAvailable Models:');
      modelsList.forEach(({ provider, models }) => {
        console.log(chalk.blue(`\n${provider}:`));
        models.forEach(model => {
          console.log(`  • ${model}`);
        });
      });
    } catch (error) {
      this.logger.error(`Failed to list models: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async pullModel(modelName: string, providerName?: string): Promise<void> {
    if (!providerName || providerName === 'ollama') {
      const provider = this.aiManager.getProvider('ollama');
      if (provider && 'pullModel' in provider) {
        try {
          this.logger.loading(`Pulling model: ${modelName}`);
          await (provider as any).pullModel(modelName);
          this.logger.success(`Model ${modelName} pulled successfully`);
        } catch (error) {
          this.logger.error(`Failed to pull model: ${error instanceof Error ? error.message : String(error)}`);
        }
      } else {
        this.logger.error('Ollama provider not available');
      }
    } else {
      this.logger.error('Model pulling is only supported for Ollama');
    }
  }

  private async removeModel(modelName: string, providerName?: string): Promise<void> {
    this.logger.warn('Model removal not yet implemented');
  }

  public async handleTraining(options: any): Promise<void> {
    if (options.generate) {
      this.logger.loading('Generating training data from codebase...');
      await this.trainingManager.generateTrainingFromCodebase();
      await this.trainingManager.trainFromInteractions();
    } else if (options.export) {
      const exportPath = await this.trainingManager.exportTrainingDataset();
      this.logger.success(`Training dataset exported to: ${exportPath}`);
    } else if (options.import) {
      await this.trainingManager.importTrainingDataset(options.import);
    } else if (options.optimize) {
      await this.trainingManager.optimizeModel();
    } else if (options.stats) {
      const stats = this.trainingManager.getTrainingStats();

      console.log(boxen(
        `Training Statistics\n\n` +
        `Total Examples: ${stats.totalExamples}\n` +
        `Positive: ${stats.positiveExamples}\n` +
        `Negative: ${stats.negativeExamples}\n` +
        `Neutral: ${stats.neutralExamples}\n` +
        `Accuracy: ${(stats.accuracy * 100).toFixed(1)}%\n` +
        `Sessions: ${stats.sessions}`,
        { padding: 1, borderColor: 'green', title: 'Training Stats' }
      ));
    } else {
      // Interactive training menu
      await this.showTrainingMenu();
    }
  }

  private async showTrainingMenu(): Promise<void> {
    const choices = [
      'Generate training data from codebase',
      'View training statistics',
      'Export training dataset',
      'Import training dataset',
      'Optimize model',
      'Start training session',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Training & Learning Options:',
        choices
      }
    ]);

    switch (action) {
      case 'Generate training data from codebase':
        await this.trainingManager.generateTrainingFromCodebase();
        break;
      case 'View training statistics':
        await this.handleTraining({ stats: true });
        break;
      case 'Export training dataset':
        const exportPath = await this.trainingManager.exportTrainingDataset();
        this.logger.success(`Exported to: ${exportPath}`);
        break;
      case 'Import training dataset':
        const { filePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'filePath',
            message: 'Enter path to training dataset file:'
          }
        ]);
        if (filePath) {
          await this.trainingManager.importTrainingDataset(filePath);
        }
        break;
      case 'Optimize model':
        await this.trainingManager.optimizeModel();
        break;
      case 'Start training session':
        const sessionId = await this.trainingManager.startTrainingSession();
        this.logger.info(`Training session started: ${sessionId}`);
        break;
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
