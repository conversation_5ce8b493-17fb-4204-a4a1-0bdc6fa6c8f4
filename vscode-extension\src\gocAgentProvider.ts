import * as vscode from 'vscode';
import axios from 'axios';

interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

interface AIProvider {
    name: string;
    baseUrl: string;
    apiKey?: string;
    defaultModel: string;
}

export class GOCAgentProvider {
    private outputChannel: vscode.OutputChannel;
    private currentProvider: string;
    private providers: Map<string, AIProvider>;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('GOC Agent');
        this.currentProvider = this.getConfig('defaultProvider') || 'ollama';
        this.providers = new Map();
        this.initializeProviders();
    }

    private initializeProviders(): void {
        this.providers.set('ollama', {
            name: 'Ollama',
            baseUrl: this.getConfig('ollamaUrl') || 'http://localhost:11434',
            defaultModel: this.getConfig('ollamaModel') || 'llama3.2'
        });

        this.providers.set('openai', {
            name: 'OpenAI',
            baseUrl: 'https://api.openai.com/v1',
            apiKey: this.getConfig('openaiApiKey'),
            defaultModel: 'gpt-4'
        });

        this.providers.set('groq', {
            name: 'Groq',
            baseUrl: 'https://api.groq.com/openai/v1',
            apiKey: this.getConfig('groqApiKey'),
            defaultModel: 'llama-3.1-70b-versatile'
        });

        this.providers.set('gemini', {
            name: 'Gemini',
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
            apiKey: this.getConfig('geminiApiKey'),
            defaultModel: 'gemini-pro'
        });
    }

    public async startAgent(task: string): Promise<void> {
        this.outputChannel.show();
        this.outputChannel.appendLine(`🤖 Starting Agent Mode`);
        this.outputChannel.appendLine(`📋 Task: ${task}`);

        try {
            // Get codebase context
            const context = await this.buildCodebaseContext();
            
            // Plan the task
            const plan = await this.planTask(task, context);
            this.outputChannel.appendLine(`📝 Plan: ${plan}`);

            // Execute with user confirmation
            const proceed = await vscode.window.showInformationMessage(
                `Agent will execute: ${plan}`,
                'Proceed', 'Cancel'
            );

            if (proceed === 'Proceed') {
                await this.executeTask(task, context);
            }
        } catch (error) {
            this.outputChannel.appendLine(`❌ Error: ${error}`);
            vscode.window.showErrorMessage(`Agent failed: ${error}`);
        }
    }

    public async startAutoAgent(goal: string): Promise<void> {
        this.outputChannel.show();
        this.outputChannel.appendLine(`🚀 Starting Auto Agent Mode`);
        this.outputChannel.appendLine(`🎯 Goal: ${goal}`);

        try {
            const context = await this.buildCodebaseContext();
            await this.executeAutoTask(goal, context);
        } catch (error) {
            this.outputChannel.appendLine(`❌ Error: ${error}`);
            vscode.window.showErrorMessage(`Auto agent failed: ${error}`);
        }
    }

    public async analyzeCodebase(): Promise<void> {
        this.outputChannel.show();
        this.outputChannel.appendLine(`📊 Analyzing codebase...`);

        try {
            const context = await this.buildCodebaseContext();
            const analysis = await this.analyzeWithAI(context);
            
            this.outputChannel.appendLine(`Analysis:\n${analysis}`);
            
            // Show in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: analysis,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
        } catch (error) {
            this.outputChannel.appendLine(`❌ Error: ${error}`);
            vscode.window.showErrorMessage(`Analysis failed: ${error}`);
        }
    }

    public async explainCode(fileName: string, selectedText?: string, selection?: vscode.Selection): Promise<void> {
        this.outputChannel.show();
        this.outputChannel.appendLine(`🔍 Explaining code in ${fileName}`);

        try {
            const document = await vscode.workspace.openTextDocument(fileName);
            const code = selectedText || document.getText();
            
            const explanation = await this.explainWithAI(code, fileName);
            
            // Show explanation in a new document
            const doc = await vscode.workspace.openTextDocument({
                content: `# Code Explanation: ${fileName}\n\n${explanation}`,
                language: 'markdown'
            });
            await vscode.window.showTextDocument(doc, vscode.ViewColumn.Beside);
        } catch (error) {
            this.outputChannel.appendLine(`❌ Error: ${error}`);
            vscode.window.showErrorMessage(`Explanation failed: ${error}`);
        }
    }

    public async editFile(fileName: string, instruction: string): Promise<void> {
        this.outputChannel.show();
        this.outputChannel.appendLine(`✏️ Editing ${fileName}: ${instruction}`);

        try {
            const document = await vscode.workspace.openTextDocument(fileName);
            const originalContent = document.getText();
            
            const newContent = await this.editWithAI(originalContent, instruction, fileName);
            
            // Show diff and ask for confirmation
            const edit = new vscode.WorkspaceEdit();
            edit.replace(document.uri, new vscode.Range(0, 0, document.lineCount, 0), newContent);
            
            const proceed = await vscode.window.showInformationMessage(
                'Apply AI-generated changes?',
                'Apply', 'Show Diff', 'Cancel'
            );

            if (proceed === 'Apply') {
                await vscode.workspace.applyEdit(edit);
                this.outputChannel.appendLine(`✅ File edited successfully`);
            } else if (proceed === 'Show Diff') {
                // Create a temporary file for diff
                const tempUri = vscode.Uri.parse(`untitled:${fileName}.new`);
                const tempDoc = await vscode.workspace.openTextDocument(tempUri);
                const tempEdit = new vscode.WorkspaceEdit();
                tempEdit.insert(tempUri, new vscode.Position(0, 0), newContent);
                await vscode.workspace.applyEdit(tempEdit);
                
                await vscode.commands.executeCommand('vscode.diff', document.uri, tempUri, `${fileName} (Original ↔ AI Modified)`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`❌ Error: ${error}`);
            vscode.window.showErrorMessage(`Edit failed: ${error}`);
        }
    }

    public async selectModel(): Promise<void> {
        try {
            // Get available models for current provider
            const models = await this.getAvailableModels();
            
            const selected = await vscode.window.showQuickPick(models, {
                placeHolder: `Select model for ${this.currentProvider}`
            });

            if (selected) {
                await this.updateConfig(`${this.currentProvider}Model`, selected);
                vscode.window.showInformationMessage(`Model changed to: ${selected}`);
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to get models: ${error}`);
        }
    }

    private async buildCodebaseContext(): Promise<string> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            throw new Error('No workspace folder open');
        }

        // Get all relevant files
        const files = await vscode.workspace.findFiles(
            '**/*.{ts,js,tsx,jsx,py,java,cpp,c,cs,php,rb,go,rs}',
            '**/node_modules/**'
        );

        let context = `Codebase Analysis:\n`;
        context += `Workspace: ${workspaceFolders[0].name}\n`;
        context += `Files: ${files.length}\n\n`;

        // Add file structure
        context += `File Structure:\n`;
        files.slice(0, 20).forEach(file => {
            const relativePath = vscode.workspace.asRelativePath(file);
            context += `- ${relativePath}\n`;
        });

        return context;
    }

    private async chatWithAI(messages: ChatMessage[]): Promise<string> {
        const provider = this.providers.get(this.currentProvider);
        if (!provider) {
            throw new Error(`Provider ${this.currentProvider} not configured`);
        }

        if (this.currentProvider === 'ollama') {
            return await this.chatWithOllama(messages, provider);
        } else {
            return await this.chatWithOpenAICompatible(messages, provider);
        }
    }

    private async chatWithOllama(messages: ChatMessage[], provider: AIProvider): Promise<string> {
        const prompt = this.formatMessagesForOllama(messages);
        
        const response = await axios.post(`${provider.baseUrl}/api/generate`, {
            model: provider.defaultModel,
            prompt: prompt,
            stream: false
        });

        return response.data.response;
    }

    private async chatWithOpenAICompatible(messages: ChatMessage[], provider: AIProvider): Promise<string> {
        const headers: any = {
            'Content-Type': 'application/json'
        };

        if (provider.apiKey) {
            headers['Authorization'] = `Bearer ${provider.apiKey}`;
        }

        const response = await axios.post(`${provider.baseUrl}/chat/completions`, {
            model: provider.defaultModel,
            messages: messages,
            temperature: 0.7
        }, { headers });

        return response.data.choices[0].message.content;
    }

    private formatMessagesForOllama(messages: ChatMessage[]): string {
        return messages.map(msg => {
            switch (msg.role) {
                case 'system': return `System: ${msg.content}`;
                case 'user': return `Human: ${msg.content}`;
                case 'assistant': return `Assistant: ${msg.content}`;
            }
        }).join('\n\n') + '\n\nAssistant: ';
    }

    private async planTask(task: string, context: string): Promise<string> {
        const messages: ChatMessage[] = [
            {
                role: 'system',
                content: `You are a coding assistant. Create a brief plan for the task.\n\nContext:\n${context}`
            },
            {
                role: 'user',
                content: `Task: ${task}`
            }
        ];

        return await this.chatWithAI(messages);
    }

    private async executeTask(task: string, context: string): Promise<void> {
        // Implementation for task execution
        this.outputChannel.appendLine(`Executing task: ${task}`);
    }

    private async executeAutoTask(goal: string, context: string): Promise<void> {
        // Implementation for auto task execution
        this.outputChannel.appendLine(`Auto executing goal: ${goal}`);
    }

    private async analyzeWithAI(context: string): Promise<string> {
        const messages: ChatMessage[] = [
            {
                role: 'system',
                content: 'You are a code analyst. Analyze the codebase and provide insights.'
            },
            {
                role: 'user',
                content: context
            }
        ];

        return await this.chatWithAI(messages);
    }

    private async explainWithAI(code: string, fileName: string): Promise<string> {
        const messages: ChatMessage[] = [
            {
                role: 'system',
                content: 'You are a code explainer. Explain the code clearly and concisely.'
            },
            {
                role: 'user',
                content: `File: ${fileName}\n\nCode:\n${code}`
            }
        ];

        return await this.chatWithAI(messages);
    }

    private async editWithAI(content: string, instruction: string, fileName: string): Promise<string> {
        const messages: ChatMessage[] = [
            {
                role: 'system',
                content: 'You are a code editor. Modify the code according to the instruction and return the complete modified file.'
            },
            {
                role: 'user',
                content: `File: ${fileName}\n\nInstruction: ${instruction}\n\nCurrent content:\n${content}`
            }
        ];

        return await this.chatWithAI(messages);
    }

    private async getAvailableModels(): Promise<string[]> {
        const provider = this.providers.get(this.currentProvider);
        if (!provider) return [];

        try {
            if (this.currentProvider === 'ollama') {
                const response = await axios.get(`${provider.baseUrl}/api/tags`);
                return response.data.models?.map((m: any) => m.name) || [];
            } else {
                const headers: any = {};
                if (provider.apiKey) {
                    headers['Authorization'] = `Bearer ${provider.apiKey}`;
                }
                const response = await axios.get(`${provider.baseUrl}/models`, { headers });
                return response.data.data?.map((m: any) => m.id) || [];
            }
        } catch (error) {
            return [provider.defaultModel];
        }
    }

    private getConfig(key: string): string {
        return vscode.workspace.getConfiguration('goc-agent').get(key) || '';
    }

    private async updateConfig(key: string, value: string): Promise<void> {
        await vscode.workspace.getConfiguration('goc-agent').update(key, value, vscode.ConfigurationTarget.Global);
    }
}
