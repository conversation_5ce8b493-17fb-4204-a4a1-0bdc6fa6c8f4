import { AIProviderManager } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
export interface TaskPlan {
    mainTask: string;
    subTasks: string[];
    estimatedComplexity: 'low' | 'medium' | 'high';
    estimatedTime: string;
    dependencies: string[];
    risks: string[];
}
export declare class TaskPlanner {
    private aiManager;
    private logger;
    constructor(aiManager: AIProviderManager, logger: Logger);
    createPlan(task: string, context: string): Promise<TaskPlan>;
    refinePlan(plan: TaskPlan, feedback: string): Promise<TaskPlan>;
    adaptPlan(plan: TaskPlan, currentProgress: string[], obstacles: string[]): Promise<TaskPlan>;
    private parsePlanResponse;
    private createFallbackPlan;
}
//# sourceMappingURL=TaskPlanner.d.ts.map