import fs from 'fs';
import path from 'path';
import os from 'os';
import { Logger } from '../utils/Logger';
import { AgentAction } from './AgentCore';

export interface Experience {
  id: string;
  timestamp: Date;
  task: string;
  action: AgentAction | null;
  outcome: 'success' | 'failure';
  error?: string;
  reflection?: string;
  context?: string;
}

export interface LearningPattern {
  pattern: string;
  frequency: number;
  successRate: number;
  examples: string[];
}

export class MemoryManager {
  private logger: Logger;
  private memoryPath: string;
  private experiences: Experience[] = [];
  private patterns: Map<string, LearningPattern> = new Map();

  constructor(logger: Logger) {
    this.logger = logger;
    this.memoryPath = path.join(os.homedir(), '.goc-agent', 'memory.json');
    this.loadMemory();
  }

  public addExperience(
    task: string,
    action: AgentAction | null,
    outcome: 'success' | 'failure',
    error?: string
  ): void {
    const experience: Experience = {
      id: this.generateId(),
      timestamp: new Date(),
      task,
      action,
      outcome,
      error
    };

    this.experiences.push(experience);
    this.updatePatterns(experience);
    this.saveMemory();
    
    this.logger.debug(`💾 Stored experience: ${task} -> ${outcome}`);
  }

  public addReflection(action: AgentAction, reflection: string): void {
    const lastExperience = this.experiences[this.experiences.length - 1];
    if (lastExperience && lastExperience.action?.type === action.type) {
      lastExperience.reflection = reflection;
      this.saveMemory();
    }
  }

  public getRelevantExperiences(task: string, limit: number = 5): string {
    const relevant = this.experiences
      .filter(exp => this.calculateSimilarity(exp.task, task) > 0.3)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);

    if (relevant.length === 0) {
      return 'No relevant past experiences found.';
    }

    return relevant.map(exp => 
      `Task: ${exp.task}\nAction: ${exp.action?.type || 'none'}\nOutcome: ${exp.outcome}\n${exp.reflection ? `Reflection: ${exp.reflection}` : ''}`
    ).join('\n\n');
  }

  public getSuccessPatterns(): LearningPattern[] {
    return Array.from(this.patterns.values())
      .filter(pattern => pattern.successRate > 0.7)
      .sort((a, b) => b.successRate - a.successRate);
  }

  public getFailurePatterns(): LearningPattern[] {
    return Array.from(this.patterns.values())
      .filter(pattern => pattern.successRate < 0.3)
      .sort((a, b) => a.successRate - b.successRate);
  }

  public analyzePerformance(): {
    totalExperiences: number;
    successRate: number;
    commonFailures: string[];
    improvementSuggestions: string[];
  } {
    const total = this.experiences.length;
    const successes = this.experiences.filter(exp => exp.outcome === 'success').length;
    const successRate = total > 0 ? successes / total : 0;

    const failures = this.experiences.filter(exp => exp.outcome === 'failure');
    const commonFailures = this.extractCommonFailures(failures);

    return {
      totalExperiences: total,
      successRate,
      commonFailures,
      improvementSuggestions: this.generateImprovementSuggestions(commonFailures)
    };
  }

  public trainFromExperiences(): void {
    this.logger.info('🧠 Training from past experiences...');
    
    // Analyze patterns
    this.analyzeTaskPatterns();
    this.analyzeActionEffectiveness();
    this.identifyOptimalStrategies();
    
    this.logger.success(`✅ Training completed. Analyzed ${this.experiences.length} experiences.`);
  }

  public exportTrainingData(): any {
    return {
      experiences: this.experiences,
      patterns: Array.from(this.patterns.entries()),
      performance: this.analyzePerformance(),
      timestamp: new Date()
    };
  }

  public importTrainingData(data: any): void {
    if (data.experiences) {
      this.experiences = [...this.experiences, ...data.experiences];
    }
    
    if (data.patterns) {
      data.patterns.forEach(([key, pattern]: [string, LearningPattern]) => {
        this.patterns.set(key, pattern);
      });
    }
    
    this.saveMemory();
    this.logger.success('📥 Training data imported successfully');
  }

  private loadMemory(): void {
    try {
      if (fs.existsSync(this.memoryPath)) {
        const data = JSON.parse(fs.readFileSync(this.memoryPath, 'utf8'));
        this.experiences = data.experiences || [];
        
        if (data.patterns) {
          this.patterns = new Map(data.patterns);
        }
        
        this.logger.debug(`📚 Loaded ${this.experiences.length} experiences from memory`);
      }
    } catch (error) {
      this.logger.debug(`Failed to load memory: ${error}`);
    }
  }

  private saveMemory(): void {
    try {
      const memoryDir = path.dirname(this.memoryPath);
      if (!fs.existsSync(memoryDir)) {
        fs.mkdirSync(memoryDir, { recursive: true });
      }

      const data = {
        experiences: this.experiences,
        patterns: Array.from(this.patterns.entries()),
        lastUpdated: new Date()
      };

      fs.writeFileSync(this.memoryPath, JSON.stringify(data, null, 2));
    } catch (error) {
      this.logger.error(`Failed to save memory: ${error}`);
    }
  }

  private updatePatterns(experience: Experience): void {
    if (!experience.action) return;

    const patternKey = `${experience.action.type}:${this.extractTaskType(experience.task)}`;
    const existing = this.patterns.get(patternKey);

    if (existing) {
      existing.frequency++;
      const totalOutcomes = existing.frequency;
      const successes = existing.successRate * (totalOutcomes - 1) + (experience.outcome === 'success' ? 1 : 0);
      existing.successRate = successes / totalOutcomes;
      existing.examples.push(experience.task);
    } else {
      this.patterns.set(patternKey, {
        pattern: patternKey,
        frequency: 1,
        successRate: experience.outcome === 'success' ? 1 : 0,
        examples: [experience.task]
      });
    }
  }

  private calculateSimilarity(task1: string, task2: string): number {
    const words1 = task1.toLowerCase().split(/\s+/);
    const words2 = task2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  private extractTaskType(task: string): string {
    const keywords = ['create', 'edit', 'fix', 'implement', 'add', 'remove', 'refactor', 'test'];
    const taskLower = task.toLowerCase();
    
    for (const keyword of keywords) {
      if (taskLower.includes(keyword)) {
        return keyword;
      }
    }
    
    return 'general';
  }

  private extractCommonFailures(failures: Experience[]): string[] {
    const errorCounts = new Map<string, number>();
    
    failures.forEach(failure => {
      if (failure.error) {
        const errorType = this.categorizeError(failure.error);
        errorCounts.set(errorType, (errorCounts.get(errorType) || 0) + 1);
      }
    });
    
    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([error]) => error);
  }

  private categorizeError(error: string): string {
    if (error.includes('not found')) return 'File not found';
    if (error.includes('permission')) return 'Permission denied';
    if (error.includes('syntax')) return 'Syntax error';
    if (error.includes('network')) return 'Network error';
    return 'Unknown error';
  }

  private generateImprovementSuggestions(commonFailures: string[]): string[] {
    const suggestions: string[] = [];
    
    commonFailures.forEach(failure => {
      switch (failure) {
        case 'File not found':
          suggestions.push('Verify file paths before operations');
          break;
        case 'Permission denied':
          suggestions.push('Check file permissions and user access');
          break;
        case 'Syntax error':
          suggestions.push('Validate code syntax before writing files');
          break;
        case 'Network error':
          suggestions.push('Implement retry logic for network operations');
          break;
      }
    });
    
    return suggestions;
  }

  private analyzeTaskPatterns(): void {
    // Analyze which types of tasks are most common and successful
    const taskTypes = new Map<string, { count: number; successRate: number }>();
    
    this.experiences.forEach(exp => {
      const taskType = this.extractTaskType(exp.task);
      const existing = taskTypes.get(taskType) || { count: 0, successRate: 0 };
      
      existing.count++;
      existing.successRate = (existing.successRate * (existing.count - 1) + 
        (exp.outcome === 'success' ? 1 : 0)) / existing.count;
      
      taskTypes.set(taskType, existing);
    });
  }

  private analyzeActionEffectiveness(): void {
    // Analyze which actions are most effective for different task types
    const actionEffectiveness = new Map<string, number>();
    
    this.experiences.forEach(exp => {
      if (exp.action) {
        const key = exp.action.type;
        const existing = actionEffectiveness.get(key) || 0;
        actionEffectiveness.set(key, existing + (exp.outcome === 'success' ? 1 : -1));
      }
    });
  }

  private identifyOptimalStrategies(): void {
    // Identify sequences of actions that lead to success
    const successfulSequences = this.experiences
      .filter(exp => exp.outcome === 'success')
      .map(exp => exp.action?.type)
      .filter(Boolean);
    
    // This could be expanded to find common successful patterns
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
