"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandHandler = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const boxen_1 = __importDefault(require("boxen"));
const cli_table3_1 = __importDefault(require("cli-table3"));
const ContextBuilder_1 = require("./ContextBuilder");
const FileEditor_1 = require("./FileEditor");
const AgentCore_1 = require("../agent/AgentCore");
const MemoryManager_1 = require("../agent/MemoryManager");
const TrainingManager_1 = require("../training/TrainingManager");
class CommandHandler {
    constructor(config, codebaseAnalyzer, aiManager, logger) {
        this.config = config;
        this.codebaseAnalyzer = codebaseAnalyzer;
        this.aiManager = aiManager;
        this.logger = logger;
        this.contextBuilder = new ContextBuilder_1.ContextBuilder(codebaseAnalyzer, logger);
        this.fileEditor = new FileEditor_1.FileEditor(logger);
        this.memoryManager = new MemoryManager_1.MemoryManager(logger);
        this.trainingManager = new TrainingManager_1.TrainingManager(logger, this.memoryManager, codebaseAnalyzer);
        this.agentCore = new AgentCore_1.AgentCore(aiManager, codebaseAnalyzer, this.contextBuilder, this.fileEditor, logger);
    }
    async handleConfig() {
        const choices = [
            'View current configuration',
            'Configure AI providers',
            'Set default provider',
            'Configure codebase settings',
            'Exit'
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'What would you like to configure?',
                choices
            }
        ]);
        switch (action) {
            case 'View current configuration':
                await this.showCurrentConfig();
                break;
            case 'Configure AI providers':
                await this.configureProviders();
                break;
            case 'Set default provider':
                await this.setDefaultProvider();
                break;
            case 'Configure codebase settings':
                await this.configureCodebase();
                break;
        }
    }
    async handleChat(options) {
        const providerName = options.provider;
        const model = options.model;
        // Check if provider is available
        if (providerName) {
            const provider = this.aiManager.getProvider(providerName);
            if (!provider) {
                this.logger.error(`Provider '${providerName}' not found or not configured`);
                return;
            }
        }
        this.logger.info('Starting interactive chat session...');
        this.logger.info('Type "exit" to quit, "help" for commands');
        const messages = [];
        // Add system context
        const context = await this.contextBuilder.buildContext();
        if (context) {
            messages.push({
                role: 'system',
                content: context
            });
        }
        while (true) {
            const { input } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'input',
                    message: chalk_1.default.green('You:'),
                }
            ]);
            if (input.toLowerCase() === 'exit') {
                break;
            }
            if (input.toLowerCase() === 'help') {
                this.showChatHelp();
                continue;
            }
            if (input.trim() === '') {
                continue;
            }
            messages.push({
                role: 'user',
                content: input
            });
            try {
                this.logger.loading('Thinking...');
                const response = await this.aiManager.chat(messages, providerName, model);
                console.log(chalk_1.default.blue('Assistant:'), response.content);
                if (response.usage) {
                    this.logger.debug(`Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`);
                }
                messages.push({
                    role: 'assistant',
                    content: response.content
                });
            }
            catch (error) {
                this.logger.error(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
            }
        }
        this.logger.info('Chat session ended');
    }
    async handleAnalyze(options) {
        this.logger.loading('Analyzing codebase...');
        try {
            const context = await this.codebaseAnalyzer.scanCodebase();
            console.log((0, boxen_1.default)(`Codebase Analysis\n\n` +
                `Files: ${context.totalFiles}\n` +
                `Total Size: ${this.formatBytes(context.totalSize)}\n` +
                `Languages: ${context.languages.join(', ')}\n`, { padding: 1, borderColor: 'blue' }));
            if (options.files) {
                const files = await this.codebaseAnalyzer.searchFiles(options.files);
                console.log(`\nMatching files (${files.length}):`);
                files.forEach(file => {
                    console.log(`  ${file.relativePath}`);
                });
            }
            else {
                console.log('\nProject Structure:');
                console.log(context.structure);
            }
        }
        catch (error) {
            this.logger.error(`Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleEdit(file, options) {
        if (!options.instruction) {
            const { instruction } = await inquirer_1.default.prompt([
                {
                    type: 'input',
                    name: 'instruction',
                    message: 'What changes would you like to make?',
                    validate: (input) => input.trim() !== '' || 'Please provide an instruction'
                }
            ]);
            options.instruction = instruction;
        }
        try {
            await this.fileEditor.editFile(file, options.instruction, this.aiManager, this.contextBuilder);
            this.logger.success(`File ${file} edited successfully`);
        }
        catch (error) {
            this.logger.error(`Edit failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleExplain(file, options) {
        try {
            const content = await this.codebaseAnalyzer.getFileContent(file);
            const context = await this.contextBuilder.buildFileContext(file);
            let prompt = `Please explain this code:\n\n${content}`;
            if (options.lines) {
                const [start, end] = options.lines.split('-').map(Number);
                const lines = content.split('\n');
                const selectedLines = lines.slice(start - 1, end).join('\n');
                prompt = `Please explain these lines (${start}-${end}) from ${file}:\n\n${selectedLines}`;
            }
            const messages = [
                { role: 'system', content: context },
                { role: 'user', content: prompt }
            ];
            this.logger.loading('Analyzing code...');
            const response = await this.aiManager.chat(messages);
            console.log((0, boxen_1.default)(response.content, {
                padding: 1,
                borderColor: 'green',
                title: `Code Explanation: ${file}`
            }));
        }
        catch (error) {
            this.logger.error(`Explanation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleSearch(query, options) {
        try {
            const searchType = options.type || 'text';
            const results = await this.codebaseAnalyzer.searchFiles(query, searchType);
            if (results.length === 0) {
                this.logger.info('No matches found');
                return;
            }
            console.log(`\nFound ${results.length} matches:`);
            results.forEach(file => {
                console.log(`  ${chalk_1.default.blue(file.relativePath)} (${file.size} bytes)`);
            });
        }
        catch (error) {
            this.logger.error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleExec(command) {
        this.logger.info(`Executing: ${command}`);
        // TODO: Implement shell command execution with AI assistance
        this.logger.warn('Command execution not yet implemented');
    }
    async handleAgent(options) {
        const task = options.task || await this.promptForTask();
        this.logger.info(`🤖 Starting Agent Mode`);
        this.logger.info(`📋 Task: ${task}`);
        try {
            await this.agentCore.startAgent(task, options);
        }
        catch (error) {
            this.logger.error(`Agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleAutoAgent(options) {
        const goal = options.goal || await this.promptForGoal();
        this.logger.info(`🚀 Starting Auto Agent Mode`);
        this.logger.info(`🎯 Goal: ${goal}`);
        const { confirm } = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: chalk_1.default.yellow('⚠️  Auto mode will make autonomous changes to your codebase. Continue?'),
                default: false
            }
        ]);
        if (!confirm) {
            this.logger.info('Auto agent mode cancelled');
            return;
        }
        try {
            await this.agentCore.startAutoAgent(goal, options);
        }
        catch (error) {
            this.logger.error(`Auto agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async handleModels(options) {
        if (options.pull) {
            await this.pullModel(options.pull, options.provider);
        }
        else if (options.remove) {
            await this.removeModel(options.remove, options.provider);
        }
        else {
            await this.listModels(options.provider);
        }
    }
    async showCurrentConfig() {
        const config = this.config.getConfig();
        console.log((0, boxen_1.default)(`Current Configuration\n\n` +
            `Default Provider: ${config.defaultProvider}\n` +
            `Config Path: ${this.config.getConfigPath()}\n`, { padding: 1, borderColor: 'cyan' }));
        const table = new cli_table3_1.default({
            head: ['Provider', 'Status', 'Model', 'URL'],
            colWidths: [12, 12, 20, 40]
        });
        for (const [name, provider] of Object.entries(config.providers)) {
            const status = provider.enabled ?
                (provider.apiKey || name === 'ollama' ? chalk_1.default.green('✓ Ready') : chalk_1.default.yellow('⚠ No API Key')) :
                chalk_1.default.red('✗ Disabled');
            table.push([
                name,
                status,
                provider.defaultModel || 'N/A',
                provider.baseUrl || 'N/A'
            ]);
        }
        console.log(table.toString());
    }
    async configureProviders() {
        const config = this.config.getConfig();
        const providers = Object.keys(config.providers);
        const { provider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Which provider would you like to configure?',
                choices: providers
            }
        ]);
        const providerConfig = config.providers[provider];
        const { apiKey, enabled } = await inquirer_1.default.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: `Enter API key for ${provider}:`,
                when: provider !== 'ollama',
                default: providerConfig.apiKey
            },
            {
                type: 'confirm',
                name: 'enabled',
                message: `Enable ${provider}?`,
                default: providerConfig.enabled
            }
        ]);
        if (apiKey) {
            this.config.setProviderApiKey(provider, apiKey);
        }
        this.config.enableProvider(provider, enabled);
        this.logger.success(`${provider} configuration updated`);
    }
    async setDefaultProvider() {
        const availableProviders = await this.aiManager.getAvailableProviders();
        if (availableProviders.length === 0) {
            this.logger.error('No providers are available. Please configure at least one provider first.');
            return;
        }
        const { provider } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select default provider:',
                choices: availableProviders
            }
        ]);
        this.config.updateConfig({ defaultProvider: provider });
        this.logger.success(`Default provider set to ${provider}`);
    }
    async configureCodebase() {
        this.logger.info('Codebase configuration not yet implemented');
    }
    showChatHelp() {
        console.log((0, boxen_1.default)('Chat Commands:\n\n' +
            'exit - End the chat session\n' +
            'help - Show this help message\n', { padding: 1, borderColor: 'yellow', title: 'Help' }));
    }
    async promptForTask() {
        const { task } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'task',
                message: 'What task would you like the agent to perform?',
                validate: (input) => input.trim() !== '' || 'Please provide a task description'
            }
        ]);
        return task;
    }
    async promptForGoal() {
        const { goal } = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'goal',
                message: 'What is your high-level goal for the auto agent?',
                validate: (input) => input.trim() !== '' || 'Please provide a goal description'
            }
        ]);
        return goal;
    }
    async listModels(providerName) {
        try {
            const modelsList = await this.aiManager.listModels(providerName);
            if (modelsList.length === 0) {
                this.logger.info('No models available');
                return;
            }
            console.log('\nAvailable Models:');
            modelsList.forEach(({ provider, models }) => {
                console.log(chalk_1.default.blue(`\n${provider}:`));
                models.forEach(model => {
                    console.log(`  • ${model}`);
                });
            });
        }
        catch (error) {
            this.logger.error(`Failed to list models: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async pullModel(modelName, providerName) {
        if (!providerName || providerName === 'ollama') {
            const provider = this.aiManager.getProvider('ollama');
            if (provider && 'pullModel' in provider) {
                try {
                    this.logger.loading(`Pulling model: ${modelName}`);
                    await provider.pullModel(modelName);
                    this.logger.success(`Model ${modelName} pulled successfully`);
                }
                catch (error) {
                    this.logger.error(`Failed to pull model: ${error instanceof Error ? error.message : String(error)}`);
                }
            }
            else {
                this.logger.error('Ollama provider not available');
            }
        }
        else {
            this.logger.error('Model pulling is only supported for Ollama');
        }
    }
    async removeModel(modelName, providerName) {
        this.logger.warn('Model removal not yet implemented');
    }
    async handleTraining(options) {
        if (options.generate) {
            this.logger.loading('Generating training data from codebase...');
            await this.trainingManager.generateTrainingFromCodebase();
            await this.trainingManager.trainFromInteractions();
        }
        else if (options.export) {
            const exportPath = await this.trainingManager.exportTrainingDataset();
            this.logger.success(`Training dataset exported to: ${exportPath}`);
        }
        else if (options.import) {
            await this.trainingManager.importTrainingDataset(options.import);
        }
        else if (options.optimize) {
            await this.trainingManager.optimizeModel();
        }
        else if (options.stats) {
            const stats = this.trainingManager.getTrainingStats();
            console.log((0, boxen_1.default)(`Training Statistics\n\n` +
                `Total Examples: ${stats.totalExamples}\n` +
                `Positive: ${stats.positiveExamples}\n` +
                `Negative: ${stats.negativeExamples}\n` +
                `Neutral: ${stats.neutralExamples}\n` +
                `Accuracy: ${(stats.accuracy * 100).toFixed(1)}%\n` +
                `Sessions: ${stats.sessions}`, { padding: 1, borderColor: 'green', title: 'Training Stats' }));
        }
        else {
            // Interactive training menu
            await this.showTrainingMenu();
        }
    }
    async showTrainingMenu() {
        const choices = [
            'Generate training data from codebase',
            'View training statistics',
            'Export training dataset',
            'Import training dataset',
            'Optimize model',
            'Start training session',
            'Exit'
        ];
        const { action } = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Training & Learning Options:',
                choices
            }
        ]);
        switch (action) {
            case 'Generate training data from codebase':
                await this.trainingManager.generateTrainingFromCodebase();
                break;
            case 'View training statistics':
                await this.handleTraining({ stats: true });
                break;
            case 'Export training dataset':
                const exportPath = await this.trainingManager.exportTrainingDataset();
                this.logger.success(`Exported to: ${exportPath}`);
                break;
            case 'Import training dataset':
                const { filePath } = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'filePath',
                        message: 'Enter path to training dataset file:'
                    }
                ]);
                if (filePath) {
                    await this.trainingManager.importTrainingDataset(filePath);
                }
                break;
            case 'Optimize model':
                await this.trainingManager.optimizeModel();
                break;
            case 'Start training session':
                const sessionId = await this.trainingManager.startTrainingSession();
                this.logger.info(`Training session started: ${sessionId}`);
                break;
        }
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
exports.CommandHandler = CommandHandler;
//# sourceMappingURL=CommandHandler.js.map