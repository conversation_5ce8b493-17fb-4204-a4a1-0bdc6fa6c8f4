"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const axios_1 = __importDefault(require("axios"));
class OpenAIProvider {
    constructor(config, logger) {
        this.name = 'OpenAI';
        this.config = config;
        this.logger = logger;
        this.client = axios_1.default.create({
            baseURL: config.baseUrl || 'https://api.openai.com/v1',
            timeout: 60000,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${config.apiKey}`,
            },
        });
    }
    async isAvailable() {
        try {
            const response = await this.client.get('/models');
            return response.status === 200;
        }
        catch (error) {
            this.logger.debug(`OpenAI availability check failed: ${error}`);
            return false;
        }
    }
    async chat(messages, model) {
        try {
            const modelName = model || this.config.defaultModel || 'gpt-4';
            const response = await this.client.post('/chat/completions', {
                model: modelName,
                messages: messages,
                temperature: 0.7,
                max_tokens: 4000,
            });
            if (response.data && response.data.choices && response.data.choices.length > 0) {
                const choice = response.data.choices[0];
                return {
                    content: choice.message.content,
                    usage: {
                        promptTokens: response.data.usage?.prompt_tokens || 0,
                        completionTokens: response.data.usage?.completion_tokens || 0,
                        totalTokens: response.data.usage?.total_tokens || 0
                    }
                };
            }
            throw new Error('Invalid response from OpenAI');
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    throw new Error('Invalid OpenAI API key');
                }
                if (error.response?.status === 404) {
                    throw new Error(`Model '${model || this.config.defaultModel}' not found`);
                }
                throw new Error(`OpenAI API error: ${error.response?.data?.error?.message || error.message}`);
            }
            throw error;
        }
    }
    async listModels() {
        try {
            const response = await this.client.get('/models');
            if (response.data && response.data.data) {
                return response.data.data
                    .filter((model) => model.id.includes('gpt'))
                    .map((model) => model.id)
                    .sort();
            }
            return [];
        }
        catch (error) {
            this.logger.error(`Failed to list OpenAI models: ${error}`);
            return [];
        }
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=OpenAIProvider.js.map