"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrainingManager = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
class TrainingManager {
    constructor(logger, memoryManager, codebaseAnalyzer) {
        this.trainingData = [];
        this.sessions = [];
        this.logger = logger;
        this.memoryManager = memoryManager;
        this.codebaseAnalyzer = codebaseAnalyzer;
        this.trainingPath = path_1.default.join(os_1.default.homedir(), '.goc-agent', 'training');
        this.ensureTrainingDirectory();
        this.loadTrainingData();
    }
    async startTrainingSession() {
        const sessionId = this.generateId();
        const session = {
            id: sessionId,
            startTime: new Date(),
            totalSamples: 0,
            accuracy: 0,
            improvements: []
        };
        this.sessions.push(session);
        this.logger.info(`🎓 Started training session: ${sessionId}`);
        return sessionId;
    }
    async endTrainingSession(sessionId) {
        const session = this.sessions.find(s => s.id === sessionId);
        if (session) {
            session.endTime = new Date();
            this.saveTrainingData();
            this.logger.success(`✅ Training session completed: ${sessionId}`);
        }
    }
    async addTrainingExample(input, expectedOutput, context, tags = []) {
        const trainingExample = {
            id: this.generateId(),
            timestamp: new Date(),
            input,
            expectedOutput,
            feedback: 'neutral',
            context,
            tags
        };
        this.trainingData.push(trainingExample);
        this.saveTrainingData();
        this.logger.debug(`📝 Added training example: ${input.substring(0, 50)}...`);
    }
    async provideFeedback(exampleId, actualOutput, feedback) {
        const example = this.trainingData.find(t => t.id === exampleId);
        if (example) {
            example.actualOutput = actualOutput;
            example.feedback = feedback;
            this.saveTrainingData();
            this.logger.info(`📊 Feedback provided for example: ${feedback}`);
        }
    }
    async generateTrainingFromCodebase() {
        this.logger.info('🔍 Generating training data from codebase...');
        const context = await this.codebaseAnalyzer.scanCodebase();
        // Generate examples for different types of tasks
        await this.generateCodeExplanationExamples(context);
        await this.generateCodeEditingExamples(context);
        await this.generateCodeAnalysisExamples(context);
        this.logger.success(`✅ Generated ${this.trainingData.length} training examples`);
    }
    async trainFromInteractions() {
        this.logger.info('🧠 Training from user interactions...');
        const experiences = this.memoryManager.exportTrainingData();
        // Convert experiences to training data
        if (experiences.experiences) {
            for (const exp of experiences.experiences) {
                if (exp.action && exp.outcome === 'success') {
                    await this.addTrainingExample(exp.task, exp.action.instruction, exp.context || '', [exp.action.type, 'successful']);
                }
            }
        }
        this.logger.success('✅ Training data updated from interactions');
    }
    async exportTrainingDataset() {
        const exportPath = path_1.default.join(this.trainingPath, `training-export-${Date.now()}.json`);
        const dataset = {
            metadata: {
                exportDate: new Date(),
                totalExamples: this.trainingData.length,
                version: '1.0'
            },
            trainingData: this.trainingData,
            sessions: this.sessions,
            performance: this.calculatePerformanceMetrics()
        };
        fs_1.default.writeFileSync(exportPath, JSON.stringify(dataset, null, 2));
        this.logger.success(`📤 Training dataset exported to: ${exportPath}`);
        return exportPath;
    }
    async importTrainingDataset(filePath) {
        try {
            const data = JSON.parse(fs_1.default.readFileSync(filePath, 'utf8'));
            if (data.trainingData) {
                this.trainingData = [...this.trainingData, ...data.trainingData];
            }
            if (data.sessions) {
                this.sessions = [...this.sessions, ...data.sessions];
            }
            this.saveTrainingData();
            this.logger.success(`📥 Training dataset imported from: ${filePath}`);
        }
        catch (error) {
            this.logger.error(`Failed to import training dataset: ${error}`);
            throw error;
        }
    }
    async optimizeModel() {
        this.logger.info('⚡ Optimizing model based on training data...');
        // Analyze patterns in successful vs failed examples
        const positiveExamples = this.trainingData.filter(t => t.feedback === 'positive');
        const negativeExamples = this.trainingData.filter(t => t.feedback === 'negative');
        this.logger.info(`📊 Positive examples: ${positiveExamples.length}`);
        this.logger.info(`📊 Negative examples: ${negativeExamples.length}`);
        // Generate optimization recommendations
        const recommendations = this.generateOptimizationRecommendations(positiveExamples, negativeExamples);
        this.logger.info('💡 Optimization recommendations:');
        recommendations.forEach(rec => this.logger.info(`  • ${rec}`));
    }
    getTrainingStats() {
        const total = this.trainingData.length;
        const positive = this.trainingData.filter(t => t.feedback === 'positive').length;
        const negative = this.trainingData.filter(t => t.feedback === 'negative').length;
        const neutral = this.trainingData.filter(t => t.feedback === 'neutral').length;
        return {
            totalExamples: total,
            positiveExamples: positive,
            negativeExamples: negative,
            neutralExamples: neutral,
            accuracy: total > 0 ? positive / (positive + negative) : 0,
            sessions: this.sessions.length
        };
    }
    async generateCodeExplanationExamples(context) {
        // Generate examples for code explanation tasks
        for (const file of context.files.slice(0, 10)) {
            try {
                const content = await this.codebaseAnalyzer.getFileContent(file.path);
                const lines = content.split('\n');
                // Create examples for different parts of the file
                if (lines.length > 10) {
                    const snippet = lines.slice(0, 10).join('\n');
                    await this.addTrainingExample(`Explain this code from ${file.relativePath}`, `This code snippet from ${file.relativePath} contains...`, snippet, ['explanation', 'code-analysis']);
                }
            }
            catch (error) {
                this.logger.debug(`Failed to process file ${file.relativePath}: ${error}`);
            }
        }
    }
    async generateCodeEditingExamples(context) {
        // Generate examples for code editing tasks
        const commonEdits = [
            'Add error handling',
            'Add type annotations',
            'Optimize performance',
            'Add documentation',
            'Refactor for readability'
        ];
        for (const edit of commonEdits) {
            await this.addTrainingExample(edit, `Applied ${edit.toLowerCase()} to the code`, 'Code editing context', ['editing', 'improvement']);
        }
    }
    async generateCodeAnalysisExamples(context) {
        // Generate examples for code analysis tasks
        await this.addTrainingExample('Analyze the codebase architecture', `This codebase follows a ${context.languages.join(', ')} architecture with ${context.totalFiles} files...`, JSON.stringify(context), ['analysis', 'architecture']);
    }
    calculatePerformanceMetrics() {
        const stats = this.getTrainingStats();
        return {
            accuracy: stats.accuracy,
            totalExamples: stats.totalExamples,
            feedbackDistribution: {
                positive: stats.positiveExamples,
                negative: stats.negativeExamples,
                neutral: stats.neutralExamples
            },
            improvementTrend: this.calculateImprovementTrend()
        };
    }
    calculateImprovementTrend() {
        // Calculate improvement over time
        const sortedData = this.trainingData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
        const windowSize = 10;
        const trend = [];
        for (let i = windowSize; i < sortedData.length; i += windowSize) {
            const window = sortedData.slice(i - windowSize, i);
            const positive = window.filter(t => t.feedback === 'positive').length;
            const accuracy = positive / window.length;
            trend.push(accuracy);
        }
        return trend;
    }
    generateOptimizationRecommendations(positive, negative) {
        const recommendations = [];
        // Analyze common patterns in positive examples
        const positiveTags = positive.flatMap(p => p.tags);
        const negativeTags = negative.flatMap(n => n.tags);
        const positiveTagCounts = this.countTags(positiveTags);
        const negativeTagCounts = this.countTags(negativeTags);
        // Generate recommendations based on patterns
        Object.entries(positiveTagCounts).forEach(([tag, count]) => {
            if (count > positive.length * 0.5) {
                recommendations.push(`Focus more on ${tag} tasks - high success rate`);
            }
        });
        Object.entries(negativeTagCounts).forEach(([tag, count]) => {
            if (count > negative.length * 0.5) {
                recommendations.push(`Improve handling of ${tag} tasks - high failure rate`);
            }
        });
        return recommendations;
    }
    countTags(tags) {
        return tags.reduce((acc, tag) => {
            acc[tag] = (acc[tag] || 0) + 1;
            return acc;
        }, {});
    }
    ensureTrainingDirectory() {
        if (!fs_1.default.existsSync(this.trainingPath)) {
            fs_1.default.mkdirSync(this.trainingPath, { recursive: true });
        }
    }
    loadTrainingData() {
        try {
            const dataPath = path_1.default.join(this.trainingPath, 'training-data.json');
            if (fs_1.default.existsSync(dataPath)) {
                const data = JSON.parse(fs_1.default.readFileSync(dataPath, 'utf8'));
                this.trainingData = data.trainingData || [];
                this.sessions = data.sessions || [];
            }
        }
        catch (error) {
            this.logger.debug(`Failed to load training data: ${error}`);
        }
    }
    saveTrainingData() {
        try {
            const dataPath = path_1.default.join(this.trainingPath, 'training-data.json');
            const data = {
                trainingData: this.trainingData,
                sessions: this.sessions,
                lastUpdated: new Date()
            };
            fs_1.default.writeFileSync(dataPath, JSON.stringify(data, null, 2));
        }
        catch (error) {
            this.logger.error(`Failed to save training data: ${error}`);
        }
    }
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
exports.TrainingManager = TrainingManager;
//# sourceMappingURL=TrainingManager.js.map