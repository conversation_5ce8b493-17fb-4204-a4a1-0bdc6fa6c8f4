#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { ConfigManager } from './config/ConfigManager';
import { CodebaseAnalyzer } from './codebase/CodebaseAnalyzer';
import { AIProviderManager } from './ai/AIProviderManager';
import { CommandHandler } from './commands/CommandHandler';
import { Logger } from './utils/Logger';

const program = new Command();

async function main() {
  try {
    // Initialize core components
    const config = new ConfigManager();
    const logger = new Logger();
    const codebaseAnalyzer = new CodebaseAnalyzer(config, logger);
    const aiManager = new AIProviderManager(config, logger);
    const commandHandler = new CommandHandler(config, codebaseAnalyzer, aiManager, logger);

    // Setup CLI
    program
      .name('goc')
      .description('GOC Agent - A coding agent with multiple AI provider support')
      .version('1.0.0');

    // Configuration commands
    program
      .command('config')
      .description('Configure AI providers and settings')
      .action(async () => {
        await commandHandler.handleConfig();
      });

    // Chat command
    program
      .command('chat')
      .description('Start an interactive chat session')
      .option('-p, --provider <provider>', 'AI provider to use (ollama, openai, groq, gemini)')
      .option('-m, --model <model>', 'Model to use')
      .action(async (options) => {
        await commandHandler.handleChat(options);
      });

    // Agent mode - autonomous task execution
    program
      .command('agent')
      .description('Start autonomous agent mode')
      .option('-p, --provider <provider>', 'AI provider to use')
      .option('-m, --model <model>', 'Model to use')
      .option('-t, --task <task>', 'Initial task description')
      .option('--auto', 'Enable auto mode (fully autonomous)')
      .action(async (options) => {
        await commandHandler.handleAgent(options);
      });

    // Auto agent mode - fully automated coding
    program
      .command('auto')
      .description('Start fully autonomous coding mode')
      .option('-p, --provider <provider>', 'AI provider to use')
      .option('-m, --model <model>', 'Model to use')
      .option('-g, --goal <goal>', 'High-level goal for the agent')
      .option('--max-iterations <number>', 'Maximum iterations', '10')
      .action(async (options) => {
        await commandHandler.handleAutoAgent(options);
      });

    // Model management
    program
      .command('models')
      .description('Manage AI models')
      .option('-p, --provider <provider>', 'Provider to list models for')
      .option('--pull <model>', 'Pull/download a model (Ollama)')
      .option('--remove <model>', 'Remove a model (Ollama)')
      .action(async (options) => {
        await commandHandler.handleModels(options);
      });

    // Code analysis commands
    program
      .command('analyze')
      .description('Analyze the current codebase')
      .option('-f, --files <pattern>', 'File pattern to analyze')
      .action(async (options) => {
        await commandHandler.handleAnalyze(options);
      });

    // File editing commands
    program
      .command('edit <file>')
      .description('Edit a file with AI assistance')
      .option('-i, --instruction <instruction>', 'Editing instruction')
      .action(async (file, options) => {
        await commandHandler.handleEdit(file, options);
      });

    // Code explanation
    program
      .command('explain <file>')
      .description('Explain code in a file')
      .option('-l, --lines <range>', 'Line range to explain (e.g., 10-20)')
      .action(async (file, options) => {
        await commandHandler.handleExplain(file, options);
      });

    // Search command
    program
      .command('search <query>')
      .description('Search through the codebase')
      .option('-t, --type <type>', 'Search type (semantic, text, regex)')
      .action(async (query, options) => {
        await commandHandler.handleSearch(query, options);
      });

    // Execute shell commands
    program
      .command('exec <command>')
      .description('Execute shell command with AI assistance')
      .action(async (command) => {
        await commandHandler.handleExec(command);
      });

    // Training commands
    program
      .command('train')
      .description('Training and learning commands')
      .option('--generate', 'Generate training data from codebase')
      .option('--export <path>', 'Export training dataset')
      .option('--import <path>', 'Import training dataset')
      .option('--optimize', 'Optimize model based on training data')
      .option('--stats', 'Show training statistics')
      .action(async (options) => {
        await commandHandler.handleTraining(options);
      });

    // Parse arguments
    await program.parseAsync(process.argv);

  } catch (error) {
    console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error);
  process.exit(1);
});

if (require.main === module) {
  main();
}

export { main };
