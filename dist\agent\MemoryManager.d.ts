import { Logger } from '../utils/Logger';
import { AgentAction } from './AgentCore';
export interface Experience {
    id: string;
    timestamp: Date;
    task: string;
    action: AgentAction | null;
    outcome: 'success' | 'failure';
    error?: string;
    reflection?: string;
    context?: string;
}
export interface LearningPattern {
    pattern: string;
    frequency: number;
    successRate: number;
    examples: string[];
}
export declare class MemoryManager {
    private logger;
    private memoryPath;
    private experiences;
    private patterns;
    constructor(logger: Logger);
    addExperience(task: string, action: AgentAction | null, outcome: 'success' | 'failure', error?: string): void;
    addReflection(action: AgentAction, reflection: string): void;
    getRelevantExperiences(task: string, limit?: number): string;
    getSuccessPatterns(): LearningPattern[];
    getFailurePatterns(): LearningPattern[];
    analyzePerformance(): {
        totalExperiences: number;
        successRate: number;
        commonFailures: string[];
        improvementSuggestions: string[];
    };
    trainFromExperiences(): void;
    exportTrainingData(): any;
    importTrainingData(data: any): void;
    private loadMemory;
    private saveMemory;
    private updatePatterns;
    private calculateSimilarity;
    private extractTaskType;
    private extractCommonFailures;
    private categorizeError;
    private generateImprovementSuggestions;
    private analyzeTaskPatterns;
    private analyzeActionEffectiveness;
    private identifyOptimalStrategies;
    private generateId;
}
//# sourceMappingURL=MemoryManager.d.ts.map