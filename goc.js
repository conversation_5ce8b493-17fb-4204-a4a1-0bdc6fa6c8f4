#!/usr/bin/env node

// GOC Agent - Intelligent Coding Assistant
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if built version exists
const cliPath = path.join(__dirname, 'dist', 'cli.js');
if (!fs.existsSync(cliPath)) {
  console.error('❌ GOC Agent not built. Please run: npm run build');
  process.exit(1);
}

const args = process.argv.slice(2);

// If no arguments provided, show a friendly welcome
if (args.length === 0) {
  console.log(`
🤖 GOC Agent - Intelligent Coding Assistant

Usage:
  goc chat                    # Start interactive chat
  goc agent                   # Start autonomous agent mode
  goc auto                    # Start fully autonomous mode
  goc analyze                 # Analyze your codebase
  goc config                  # Configure AI providers
  goc models                  # Manage AI models
  goc --help                  # Show all commands

Quick Start:
  1. Configure a provider: goc config
  2. Start chatting: goc chat
  3. Or go autonomous: goc agent

For Ollama users:
  goc models --pull llama3.2  # Pull a model
  goc chat -p ollama -m llama3.2
`);
  process.exit(0);
}

// Run the CLI
const child = spawn('node', [cliPath, ...args], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('exit', (code) => {
  process.exit(code);
});

child.on('error', (error) => {
  console.error('❌ Failed to start GOC Agent:', error.message);
  process.exit(1);
});
