#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const ConfigManager_1 = require("./config/ConfigManager");
const CodebaseAnalyzer_1 = require("./codebase/CodebaseAnalyzer");
const AIProviderManager_1 = require("./ai/AIProviderManager");
const CommandHandler_1 = require("./commands/CommandHandler");
const Logger_1 = require("./utils/Logger");
const program = new commander_1.Command();
async function main() {
    try {
        // Initialize core components
        const config = new ConfigManager_1.ConfigManager();
        const logger = new Logger_1.Logger();
        const codebaseAnalyzer = new CodebaseAnalyzer_1.CodebaseAnalyzer(config, logger);
        const aiManager = new AIProviderManager_1.AIProviderManager(config, logger);
        const commandHandler = new CommandHandler_1.CommandHandler(config, codebaseAnalyzer, aiManager, logger);
        // Setup CLI
        program
            .name('goc')
            .description('GOC Agent - A coding agent with multiple AI provider support')
            .version('1.0.0');
        // Configuration commands
        program
            .command('config')
            .description('Configure AI providers and settings')
            .action(async () => {
            await commandHandler.handleConfig();
        });
        // Chat command
        program
            .command('chat')
            .description('Start an interactive chat session')
            .option('-p, --provider <provider>', 'AI provider to use (ollama, openai, groq, gemini)')
            .option('-m, --model <model>', 'Model to use')
            .action(async (options) => {
            await commandHandler.handleChat(options);
        });
        // Code analysis commands
        program
            .command('analyze')
            .description('Analyze the current codebase')
            .option('-f, --files <pattern>', 'File pattern to analyze')
            .action(async (options) => {
            await commandHandler.handleAnalyze(options);
        });
        // File editing commands
        program
            .command('edit <file>')
            .description('Edit a file with AI assistance')
            .option('-i, --instruction <instruction>', 'Editing instruction')
            .action(async (file, options) => {
            await commandHandler.handleEdit(file, options);
        });
        // Code explanation
        program
            .command('explain <file>')
            .description('Explain code in a file')
            .option('-l, --lines <range>', 'Line range to explain (e.g., 10-20)')
            .action(async (file, options) => {
            await commandHandler.handleExplain(file, options);
        });
        // Search command
        program
            .command('search <query>')
            .description('Search through the codebase')
            .option('-t, --type <type>', 'Search type (semantic, text, regex)')
            .action(async (query, options) => {
            await commandHandler.handleSearch(query, options);
        });
        // Execute shell commands
        program
            .command('exec <command>')
            .description('Execute shell command with AI assistance')
            .action(async (command) => {
            await commandHandler.handleExec(command);
        });
        // Parse arguments
        await program.parseAsync(process.argv);
    }
    catch (error) {
        console.error(chalk_1.default.red('Error:'), error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}
// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk_1.default.red('Unhandled Rejection at:'), promise, chalk_1.default.red('reason:'), reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(chalk_1.default.red('Uncaught Exception:'), error);
    process.exit(1);
});
if (require.main === module) {
    main();
}
//# sourceMappingURL=cli.js.map