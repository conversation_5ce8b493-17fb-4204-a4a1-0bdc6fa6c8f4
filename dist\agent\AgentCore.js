"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCore = void 0;
const TaskPlanner_1 = require("./TaskPlanner");
const ActionExecutor_1 = require("./ActionExecutor");
const MemoryManager_1 = require("./MemoryManager");
class AgentCore {
    constructor(aiManager, codebaseAnalyzer, contextBuilder, fileEditor, logger) {
        this.aiManager = aiManager;
        this.codebaseAnalyzer = codebaseAnalyzer;
        this.contextBuilder = contextBuilder;
        this.fileEditor = fileEditor;
        this.logger = logger;
        this.taskPlanner = new TaskPlanner_1.TaskPlanner(aiManager, logger);
        this.actionExecutor = new ActionExecutor_1.ActionExecutor(codebaseAnalyzer, fileEditor, logger);
        this.memoryManager = new MemoryManager_1.MemoryManager(logger);
        this.state = this.initializeState();
    }
    initializeState() {
        return {
            currentTask: '',
            subTasks: [],
            completedActions: [],
            context: '',
            workingMemory: new Map(),
            iteration: 0,
            maxIterations: 10
        };
    }
    async startAgent(task, options = {}) {
        this.logger.info(`🤖 Starting Agent Mode`);
        this.logger.info(`📋 Task: ${task}`);
        this.state.currentTask = task;
        this.state.maxIterations = parseInt(options.maxIterations) || 10;
        this.state.context = await this.contextBuilder.buildContext();
        // Plan the task
        await this.planTask(task);
        // Execute the plan
        await this.executePlan(options.auto || false);
    }
    async startAutoAgent(goal, options = {}) {
        this.logger.info(`🚀 Starting Auto Agent Mode`);
        this.logger.info(`🎯 Goal: ${goal}`);
        this.state.currentTask = goal;
        this.state.maxIterations = parseInt(options.maxIterations) || 20;
        this.state.context = await this.contextBuilder.buildContext();
        // Auto mode - fully autonomous
        await this.autonomousExecution(goal);
    }
    async planTask(task) {
        this.logger.loading('🧠 Planning task...');
        const plan = await this.taskPlanner.createPlan(task, this.state.context);
        this.state.subTasks = plan.subTasks;
        this.logger.info(`📝 Created plan with ${plan.subTasks.length} sub-tasks:`);
        plan.subTasks.forEach((subTask, index) => {
            this.logger.info(`  ${index + 1}. ${subTask}`);
        });
    }
    async executePlan(autoMode = false) {
        for (let i = 0; i < this.state.subTasks.length && this.state.iteration < this.state.maxIterations; i++) {
            const subTask = this.state.subTasks[i];
            this.state.iteration++;
            this.logger.info(`\n🔄 Iteration ${this.state.iteration}: ${subTask}`);
            try {
                // Decide on action
                const action = await this.decideAction(subTask);
                if (!autoMode) {
                    // Ask for confirmation in agent mode
                    const proceed = await this.askForConfirmation(action);
                    if (!proceed) {
                        this.logger.info('⏸️  Task paused by user');
                        break;
                    }
                }
                // Execute action
                await this.executeAction(action);
                // Reflect on results
                await this.reflect(action);
                // Update memory
                this.memoryManager.addExperience(subTask, action, 'success');
            }
            catch (error) {
                this.logger.error(`❌ Error executing sub-task: ${error}`);
                this.memoryManager.addExperience(subTask, null, 'failure', String(error));
                if (!autoMode) {
                    const retry = await this.askForRetry();
                    if (!retry)
                        break;
                }
            }
        }
        this.logger.success('✅ Agent execution completed');
    }
    async autonomousExecution(goal) {
        while (this.state.iteration < this.state.maxIterations) {
            this.state.iteration++;
            this.logger.info(`\n🔄 Auto Iteration ${this.state.iteration}`);
            try {
                // Analyze current state
                const currentState = await this.analyzeCurrentState();
                // Decide next action autonomously
                const action = await this.decideAutonomousAction(goal, currentState);
                if (action.type === 'complete') {
                    this.logger.success('🎉 Goal achieved!');
                    break;
                }
                // Execute action
                await this.executeAction(action);
                // Self-reflect and adapt
                await this.selfReflect(action);
                // Update memory
                this.memoryManager.addExperience(goal, action, 'success');
            }
            catch (error) {
                this.logger.error(`❌ Auto execution error: ${error}`);
                // Try to recover autonomously
                const recovery = await this.attemptRecovery(String(error));
                if (!recovery) {
                    this.logger.error('💥 Unable to recover, stopping auto mode');
                    break;
                }
            }
        }
    }
    async decideAction(task) {
        const messages = [
            {
                role: 'system',
                content: `You are an intelligent coding agent. Analyze the task and decide on the best action.

Available actions:
- analyze: Analyze code or codebase
- edit: Edit an existing file
- create: Create a new file
- search: Search through codebase
- execute: Execute a command
- plan: Create a detailed plan
- reflect: Reflect on progress

Context:
${this.state.context}

Previous actions:
${this.state.completedActions.map(a => `${a.type}: ${a.instruction}`).join('\n')}

Respond with JSON:
{
  "type": "action_type",
  "target": "file_path_or_query",
  "instruction": "detailed instruction",
  "reasoning": "why this action",
  "confidence": 0.8
}`
            },
            {
                role: 'user',
                content: `Task: ${task}`
            }
        ];
        const response = await this.aiManager.chat(messages);
        return this.parseActionResponse(response.content);
    }
    async decideAutonomousAction(goal, currentState) {
        const messages = [
            {
                role: 'system',
                content: `You are a fully autonomous coding agent. Analyze the goal and current state, then decide the next action.

You have complete autonomy to:
- Analyze and understand the codebase
- Create new files and features
- Edit existing code
- Execute commands
- Make architectural decisions

Goal: ${goal}
Current State: ${currentState}

Memory of past experiences:
${this.memoryManager.getRelevantExperiences(goal)}

Respond with JSON for the next action, or {"type": "complete"} if goal is achieved.`
            }
        ];
        const response = await this.aiManager.chat(messages);
        return this.parseActionResponse(response.content);
    }
    async executeAction(action) {
        this.logger.info(`🎬 Executing: ${action.type} - ${action.instruction}`);
        await this.actionExecutor.execute(action, this.aiManager, this.contextBuilder);
        this.state.completedActions.push(action);
    }
    async reflect(action) {
        // Simple reflection for now
        this.logger.debug(`💭 Reflecting on action: ${action.type}`);
    }
    async selfReflect(action) {
        // Advanced self-reflection for auto mode
        const messages = [
            {
                role: 'system',
                content: 'Reflect on the action taken. What was learned? What could be improved?'
            },
            {
                role: 'user',
                content: `Action taken: ${JSON.stringify(action)}`
            }
        ];
        const response = await this.aiManager.chat(messages);
        this.memoryManager.addReflection(action, response.content);
    }
    async analyzeCurrentState() {
        const context = await this.codebaseAnalyzer.scanCodebase();
        return `Files: ${context.totalFiles}, Languages: ${context.languages.join(', ')}`;
    }
    async attemptRecovery(error) {
        this.logger.info('🔧 Attempting autonomous recovery...');
        // Implement recovery logic
        return false;
    }
    parseActionResponse(response) {
        try {
            const cleaned = response.replace(/```json\n?|\n?```/g, '').trim();
            return JSON.parse(cleaned);
        }
        catch (error) {
            // Fallback action
            return {
                type: 'analyze',
                instruction: 'Analyze the current situation',
                reasoning: 'Failed to parse AI response',
                confidence: 0.5
            };
        }
    }
    async askForConfirmation(action) {
        // This would be implemented with inquirer in the command handler
        return true;
    }
    async askForRetry() {
        // This would be implemented with inquirer in the command handler
        return false;
    }
}
exports.AgentCore = AgentCore;
//# sourceMappingURL=AgentCore.js.map