import * as vscode from 'vscode';
import { GOCAgentProvider } from './gocAgentProvider';
import { ChatWebviewProvider } from './chatWebviewProvider';
import { StatusProvider } from './statusProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('GOC Agent extension is now active!');

    // Initialize providers
    const gocAgent = new GOCAgentProvider();
    const chatProvider = new ChatWebviewProvider(context.extensionUri);
    const statusProvider = new StatusProvider();

    // Register webview providers
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('goc-agent.chat', chatProvider)
    );

    // Register tree data providers
    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('goc-agent.status', statusProvider)
    );

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.chat', () => {
            chatProvider.show();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.agent', async () => {
            const task = await vscode.window.showInputBox({
                prompt: 'What task would you like the agent to perform?',
                placeHolder: 'e.g., Create a new React component for user authentication'
            });
            
            if (task) {
                await gocAgent.startAgent(task);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.auto', async () => {
            const goal = await vscode.window.showInputBox({
                prompt: 'What is your high-level goal?',
                placeHolder: 'e.g., Build a complete user management system'
            });
            
            if (goal) {
                const confirm = await vscode.window.showWarningMessage(
                    'Auto mode will make autonomous changes to your codebase. Continue?',
                    'Yes', 'No'
                );
                
                if (confirm === 'Yes') {
                    await gocAgent.startAutoAgent(goal);
                }
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.analyze', async () => {
            await gocAgent.analyzeCodebase();
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.explain', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            const selection = editor.selection;
            const text = editor.document.getText(selection);
            
            if (text) {
                await gocAgent.explainCode(editor.document.fileName, text, selection);
            } else {
                await gocAgent.explainCode(editor.document.fileName);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.edit', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showErrorMessage('No active editor');
                return;
            }

            const instruction = await vscode.window.showInputBox({
                prompt: 'What changes would you like to make?',
                placeHolder: 'e.g., Add error handling to this function'
            });

            if (instruction) {
                await gocAgent.editFile(editor.document.fileName, instruction);
            }
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.config', async () => {
            await vscode.commands.executeCommand('workbench.action.openSettings', 'goc-agent');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('goc-agent.selectModel', async () => {
            await gocAgent.selectModel();
        })
    );

    // Update status
    statusProvider.refresh();
    
    // Show welcome message
    vscode.window.showInformationMessage(
        'GOC Agent is ready! Use Ctrl+Shift+G to start chatting.',
        'Open Chat', 'Configure'
    ).then(selection => {
        if (selection === 'Open Chat') {
            vscode.commands.executeCommand('goc-agent.chat');
        } else if (selection === 'Configure') {
            vscode.commands.executeCommand('goc-agent.config');
        }
    });
}

export function deactivate() {
    console.log('GOC Agent extension is now deactivated');
}
