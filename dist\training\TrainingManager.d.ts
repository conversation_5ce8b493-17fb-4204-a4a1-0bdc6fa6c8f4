import { Logger } from '../utils/Logger';
import { MemoryManager } from '../agent/MemoryManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
export interface TrainingData {
    id: string;
    timestamp: Date;
    input: string;
    expectedOutput: string;
    actualOutput?: string;
    feedback: 'positive' | 'negative' | 'neutral';
    context: string;
    tags: string[];
}
export interface TrainingSession {
    id: string;
    startTime: Date;
    endTime?: Date;
    totalSamples: number;
    accuracy: number;
    improvements: string[];
}
export declare class TrainingManager {
    private logger;
    private memoryManager;
    private codebaseAnalyzer;
    private trainingPath;
    private trainingData;
    private sessions;
    constructor(logger: Logger, memoryManager: MemoryManager, codebaseAnalyzer: CodebaseAnalyzer);
    startTrainingSession(): Promise<string>;
    endTrainingSession(sessionId: string): Promise<void>;
    addTrainingExample(input: string, expectedOutput: string, context: string, tags?: string[]): Promise<void>;
    provideFeedback(exampleId: string, actualOutput: string, feedback: 'positive' | 'negative' | 'neutral'): Promise<void>;
    generateTrainingFromCodebase(): Promise<void>;
    trainFromInteractions(): Promise<void>;
    exportTrainingDataset(): Promise<string>;
    importTrainingDataset(filePath: string): Promise<void>;
    optimizeModel(): Promise<void>;
    getTrainingStats(): any;
    private generateCodeExplanationExamples;
    private generateCodeEditingExamples;
    private generateCodeAnalysisExamples;
    private calculatePerformanceMetrics;
    private calculateImprovementTrend;
    private generateOptimizationRecommendations;
    private countTags;
    private ensureTrainingDirectory;
    private loadTrainingData;
    private saveTrainingData;
    private generateId;
}
//# sourceMappingURL=TrainingManager.d.ts.map