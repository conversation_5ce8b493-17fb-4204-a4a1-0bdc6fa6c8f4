{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;AAkKS,oBAAI;AAhKb,yCAAoC;AACpC,kDAA0B;AAC1B,0DAAuD;AACvD,kEAA+D;AAC/D,8DAA2D;AAC3D,8DAA2D;AAC3D,2CAAwC;AAExC,MAAM,OAAO,GAAG,IAAI,mBAAO,EAAE,CAAC;AAE9B,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QACH,6BAA6B;QAC7B,MAAM,MAAM,GAAG,IAAI,6BAAa,EAAE,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,eAAM,EAAE,CAAC;QAC5B,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,IAAI,qCAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAEvF,YAAY;QACZ,OAAO;aACJ,IAAI,CAAC,KAAK,CAAC;aACX,WAAW,CAAC,8DAA8D,CAAC;aAC3E,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,yBAAyB;QACzB,OAAO;aACJ,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,qCAAqC,CAAC;aAClD,MAAM,CAAC,KAAK,IAAI,EAAE;YACjB,MAAM,cAAc,CAAC,YAAY,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;QAEL,eAAe;QACf,OAAO;aACJ,OAAO,CAAC,MAAM,CAAC;aACf,WAAW,CAAC,mCAAmC,CAAC;aAChD,MAAM,CAAC,2BAA2B,EAAE,mDAAmD,CAAC;aACxF,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC;aAC7C,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEL,yCAAyC;QACzC,OAAO;aACJ,OAAO,CAAC,OAAO,CAAC;aAChB,WAAW,CAAC,6BAA6B,CAAC;aAC1C,MAAM,CAAC,2BAA2B,EAAE,oBAAoB,CAAC;aACzD,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC;aAC7C,MAAM,CAAC,mBAAmB,EAAE,0BAA0B,CAAC;aACvD,MAAM,CAAC,QAAQ,EAAE,qCAAqC,CAAC;aACvD,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEL,2CAA2C;QAC3C,OAAO;aACJ,OAAO,CAAC,MAAM,CAAC;aACf,WAAW,CAAC,oCAAoC,CAAC;aACjD,MAAM,CAAC,2BAA2B,EAAE,oBAAoB,CAAC;aACzD,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC;aAC7C,MAAM,CAAC,mBAAmB,EAAE,+BAA+B,CAAC;aAC5D,MAAM,CAAC,2BAA2B,EAAE,oBAAoB,EAAE,IAAI,CAAC;aAC/D,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEL,mBAAmB;QACnB,OAAO;aACJ,OAAO,CAAC,QAAQ,CAAC;aACjB,WAAW,CAAC,kBAAkB,CAAC;aAC/B,MAAM,CAAC,2BAA2B,EAAE,6BAA6B,CAAC;aAClE,MAAM,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;aAC1D,MAAM,CAAC,kBAAkB,EAAE,yBAAyB,CAAC;aACrD,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEL,yBAAyB;QACzB,OAAO;aACJ,OAAO,CAAC,SAAS,CAAC;aAClB,WAAW,CAAC,8BAA8B,CAAC;aAC3C,MAAM,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;aAC1D,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEL,wBAAwB;QACxB,OAAO;aACJ,OAAO,CAAC,aAAa,CAAC;aACtB,WAAW,CAAC,gCAAgC,CAAC;aAC7C,MAAM,CAAC,iCAAiC,EAAE,qBAAqB,CAAC;aAChE,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;YAC9B,MAAM,cAAc,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEL,mBAAmB;QACnB,OAAO;aACJ,OAAO,CAAC,gBAAgB,CAAC;aACzB,WAAW,CAAC,wBAAwB,CAAC;aACrC,MAAM,CAAC,qBAAqB,EAAE,qCAAqC,CAAC;aACpE,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;YAC9B,MAAM,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEL,iBAAiB;QACjB,OAAO;aACJ,OAAO,CAAC,gBAAgB,CAAC;aACzB,WAAW,CAAC,6BAA6B,CAAC;aAC1C,MAAM,CAAC,mBAAmB,EAAE,qCAAqC,CAAC;aAClE,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YAC/B,MAAM,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEL,yBAAyB;QACzB,OAAO;aACJ,OAAO,CAAC,gBAAgB,CAAC;aACzB,WAAW,CAAC,0CAA0C,CAAC;aACvD,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEL,oBAAoB;QACpB,OAAO;aACJ,OAAO,CAAC,OAAO,CAAC;aAChB,WAAW,CAAC,gCAAgC,CAAC;aAC7C,MAAM,CAAC,YAAY,EAAE,sCAAsC,CAAC;aAC5D,MAAM,CAAC,iBAAiB,EAAE,yBAAyB,CAAC;aACpD,MAAM,CAAC,iBAAiB,EAAE,yBAAyB,CAAC;aACpD,MAAM,CAAC,YAAY,EAAE,uCAAuC,CAAC;aAC7D,MAAM,CAAC,SAAS,EAAE,0BAA0B,CAAC;aAC7C,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxB,MAAM,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEL,kBAAkB;QAClB,MAAM,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3F,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,8BAA8B;AAC9B,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,OAAO,EAAE,eAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;IAC3F,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,CAAC;IACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC;AACT,CAAC"}