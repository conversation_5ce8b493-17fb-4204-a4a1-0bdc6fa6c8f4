{"name": "goc-agent-vscode", "displayName": "GOC Agent", "description": "Intelligent coding assistant with multiple AI provider support", "version": "1.0.0", "publisher": "goc-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Programming Languages"], "keywords": ["ai", "coding", "assistant", "ollama", "openai", "groq", "gemini"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "goc-agent.chat", "title": "Start Chat", "category": "GOC Agent"}, {"command": "goc-agent.agent", "title": "Start Agent Mode", "category": "GOC Agent"}, {"command": "goc-agent.auto", "title": "Start Auto Mode", "category": "GOC Agent"}, {"command": "goc-agent.analyze", "title": "Analyze Codebase", "category": "GOC Agent"}, {"command": "goc-agent.explain", "title": "Explain Code", "category": "GOC Agent"}, {"command": "goc-agent.edit", "title": "Edit with AI", "category": "GOC Agent"}, {"command": "goc-agent.config", "title": "Configure", "category": "GOC Agent"}, {"command": "goc-agent.selectModel", "title": "Select Model", "category": "GOC Agent"}], "menus": {"editor/context": [{"command": "goc-agent.explain", "group": "goc-agent", "when": "editorHasSelection"}, {"command": "goc-agent.edit", "group": "goc-agent"}], "explorer/context": [{"command": "goc-agent.analyze", "group": "goc-agent"}]}, "keybindings": [{"command": "goc-agent.chat", "key": "ctrl+shift+g", "mac": "cmd+shift+g"}, {"command": "goc-agent.explain", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorHasSelection"}], "viewsContainers": {"activitybar": [{"id": "goc-agent", "title": "GOC Agent", "icon": "$(robot)"}]}, "views": {"goc-agent": [{"id": "goc-agent.chat", "name": "Cha<PERSON>", "type": "webview"}, {"id": "goc-agent.status", "name": "Status"}]}, "configuration": {"title": "GOC Agent", "properties": {"goc-agent.defaultProvider": {"type": "string", "default": "ollama", "enum": ["ollama", "openai", "groq", "gemini"], "description": "Default AI provider"}, "goc-agent.ollamaUrl": {"type": "string", "default": "http://localhost:11434", "description": "Ollama server URL"}, "goc-agent.ollamaModel": {"type": "string", "default": "llama3.2", "description": "De<PERSON><PERSON> model"}, "goc-agent.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key"}, "goc-agent.groqApiKey": {"type": "string", "default": "", "description": "Groq API Key"}, "goc-agent.geminiApiKey": {"type": "string", "default": "", "description": "Gemini API Key"}, "goc-agent.autoMode": {"type": "boolean", "default": false, "description": "Enable auto mode by default"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "18.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.2"}}