{"version": 3, "file": "MemoryManager.js", "sourceRoot": "", "sources": ["../../src/agent/MemoryManager.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,4CAAoB;AAsBpB,MAAa,aAAa;IAMxB,YAAY,MAAc;QAHlB,gBAAW,GAAiB,EAAE,CAAC;QAC/B,aAAQ,GAAiC,IAAI,GAAG,EAAE,CAAC;QAGzD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QACvE,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAEM,aAAa,CAClB,IAAY,EACZ,MAA0B,EAC1B,OAA8B,EAC9B,KAAc;QAEd,MAAM,UAAU,GAAe;YAC7B,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI;YACJ,MAAM;YACN,OAAO;YACP,KAAK;SACN,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,OAAO,OAAO,EAAE,CAAC,CAAC;IACnE,CAAC;IAEM,aAAa,CAAC,MAAmB,EAAE,UAAkB;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrE,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC;YAClE,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;YACvC,IAAI,CAAC,UAAU,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAEM,sBAAsB,CAAC,IAAY,EAAE,QAAgB,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW;aAC9B,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;aAC7D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;aAC7D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEnB,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,qCAAqC,CAAC;QAC/C,CAAC;QAED,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACxB,SAAS,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,MAAM,EAAE,IAAI,IAAI,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9I,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjB,CAAC;IAEM,kBAAkB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;aAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAEM,kBAAkB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;aAC5C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAEM,kBAAkB;QAMvB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QACnF,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAE5D,OAAO;YACL,gBAAgB,EAAE,KAAK;YACvB,WAAW;YACX,cAAc;YACd,sBAAsB,EAAE,IAAI,CAAC,8BAA8B,CAAC,cAAc,CAAC;SAC5E,CAAC;IACJ,CAAC;IAEM,oBAAoB;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAEzD,mBAAmB;QACnB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kCAAkC,IAAI,CAAC,WAAW,CAAC,MAAM,eAAe,CAAC,CAAC;IAChG,CAAC;IAEM,kBAAkB;QACvB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC7C,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE;YACtC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAEM,kBAAkB,CAAC,IAAS;QACjC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAA4B,EAAE,EAAE;gBAClE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wCAAwC,CAAC,CAAC;IAChE,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC;YACH,IAAI,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;gBAE1C,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC,MAAM,0BAA0B,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,UAAU;QAChB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,YAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,IAAI,GAAG;gBACX,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC7C,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC;YAEF,YAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,UAAsB;QAC3C,IAAI,CAAC,UAAU,CAAC,MAAM;YAAE,OAAO;QAE/B,MAAM,UAAU,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACxF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC;YACzC,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1G,QAAQ,CAAC,WAAW,GAAG,SAAS,GAAG,aAAa,CAAC;YACjD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC5B,OAAO,EAAE,UAAU;gBACnB,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,UAAU,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrD,QAAQ,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,KAAa,EAAE,KAAa;QACtD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAEhD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAClE,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEnD,OAAO,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC5C,CAAC;IAEO,eAAe,CAAC,IAAY;QAClC,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QAC7F,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAErC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,OAAO,OAAO,CAAC;YACjB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,qBAAqB,CAAC,QAAsB;QAClD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE9C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACtD,WAAW,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;aACrC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,gBAAgB,CAAC;QACzD,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,mBAAmB,CAAC;QAC7D,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,cAAc,CAAC;QACpD,IAAI,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,eAAe,CAAC;QACtD,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,8BAA8B,CAAC,cAAwB;QAC7D,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,QAAQ,OAAO,EAAE,CAAC;gBAChB,KAAK,gBAAgB;oBACnB,WAAW,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBACxD,MAAM;gBACR,KAAK,mBAAmB;oBACtB,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;oBAC3D,MAAM;gBACR,KAAK,cAAc;oBACjB,WAAW,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;oBAC9D,MAAM;gBACR,KAAK,eAAe;oBAClB,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;oBACjE,MAAM;YACV,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,mBAAmB;QACzB,8DAA8D;QAC9D,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkD,CAAC;QAE5E,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;YAEzE,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjB,QAAQ,CAAC,WAAW,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;gBACjE,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;YAExD,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,0BAA0B;QAChC,oEAAoE;QACpE,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAEtD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC5B,MAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACnD,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChF,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB;QAC/B,qDAAqD;QACrD,MAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW;aACzC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC;aACxC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;aAC5B,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnB,4DAA4D;IAC9D,CAAC;IAEO,UAAU;QAChB,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE,CAAC;CACF;AAvSD,sCAuSC"}