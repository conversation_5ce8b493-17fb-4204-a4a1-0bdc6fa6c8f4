{"name": "goc-agent", "version": "1.0.0", "description": "A coding agent with support for multiple AI providers (Ollama, Groq, Gemini, ChatGPT)", "main": "dist/index.js", "bin": {"goc": "goc.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli.ts", "start": "node dist/cli.js", "watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ai", "coding", "agent", "ollama", "groq", "gemini", "chatgpt", "cli"], "author": "Your Name", "license": "MIT", "dependencies": {"commander": "^11.1.0", "inquirer": "^9.2.12", "chalk": "^5.3.0", "axios": "^1.6.2", "chokidar": "^3.5.3", "fast-glob": "^3.3.2", "ignore": "^5.3.0", "ora": "^7.0.1", "boxen": "^7.1.1", "cli-table3": "^0.6.3", "diff": "^5.1.0", "mime-types": "^2.1.35", "yaml": "^2.3.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/inquirer": "^9.0.7", "@types/diff": "^5.0.8", "@types/mime-types": "^2.1.4", "typescript": "^5.3.2", "ts-node": "^10.9.1", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}