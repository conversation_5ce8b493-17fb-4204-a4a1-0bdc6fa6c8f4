"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileEditor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const diff_1 = require("diff");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
class FileEditor {
    constructor(logger) {
        this.logger = logger;
    }
    async editFile(filePath, instruction, aiManager, contextBuilder) {
        try {
            // Check if file exists
            if (!fs_1.default.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }
            // Read current content
            const originalContent = fs_1.default.readFileSync(filePath, 'utf8');
            // Build context for the edit
            const context = await contextBuilder.buildEditContext(filePath, instruction);
            // Prepare messages for AI
            const messages = [
                {
                    role: 'system',
                    content: context
                },
                {
                    role: 'user',
                    content: `Please modify the file according to the instruction: ${instruction}`
                }
            ];
            this.logger.loading('Generating changes...');
            // Get AI response
            const response = await aiManager.chat(messages);
            // Extract code from response (assuming it's wrapped in code blocks)
            const newContent = this.extractCodeFromResponse(response.content);
            if (!newContent) {
                throw new Error('Could not extract valid code from AI response');
            }
            // Show diff
            const diff = this.createDiff(filePath, originalContent, newContent);
            console.log('\nProposed changes:');
            console.log(diff);
            // Ask for confirmation
            const { confirm } = await inquirer_1.default.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: 'Apply these changes?',
                    default: true
                }
            ]);
            if (confirm) {
                // Create backup
                const backupPath = `${filePath}.backup.${Date.now()}`;
                fs_1.default.writeFileSync(backupPath, originalContent);
                this.logger.info(`Backup created: ${backupPath}`);
                // Write new content
                fs_1.default.writeFileSync(filePath, newContent);
                this.logger.success('Changes applied successfully');
            }
            else {
                this.logger.info('Changes cancelled');
            }
        }
        catch (error) {
            throw new Error(`Failed to edit file: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    async createFile(filePath, instruction, aiManager, contextBuilder) {
        try {
            // Check if file already exists
            if (fs_1.default.existsSync(filePath)) {
                const { overwrite } = await inquirer_1.default.prompt([
                    {
                        type: 'confirm',
                        name: 'overwrite',
                        message: `File ${filePath} already exists. Overwrite?`,
                        default: false
                    }
                ]);
                if (!overwrite) {
                    this.logger.info('File creation cancelled');
                    return;
                }
            }
            // Build context
            const context = await contextBuilder.buildContext();
            // Prepare messages for AI
            const messages = [
                {
                    role: 'system',
                    content: `${context}

You are asked to create a new file. Please provide the complete file content based on the instruction.`
                },
                {
                    role: 'user',
                    content: `Create a new file at ${filePath} with the following requirements: ${instruction}`
                }
            ];
            this.logger.loading('Generating file content...');
            // Get AI response
            const response = await aiManager.chat(messages);
            // Extract code from response
            const content = this.extractCodeFromResponse(response.content);
            if (!content) {
                throw new Error('Could not extract valid code from AI response');
            }
            // Show preview
            console.log('\nGenerated content:');
            console.log(chalk_1.default.gray('─'.repeat(50)));
            console.log(content);
            console.log(chalk_1.default.gray('─'.repeat(50)));
            // Ask for confirmation
            const { confirm } = await inquirer_1.default.prompt([
                {
                    type: 'confirm',
                    name: 'confirm',
                    message: 'Create this file?',
                    default: true
                }
            ]);
            if (confirm) {
                // Ensure directory exists
                const dir = path_1.default.dirname(filePath);
                if (!fs_1.default.existsSync(dir)) {
                    fs_1.default.mkdirSync(dir, { recursive: true });
                }
                // Write file
                fs_1.default.writeFileSync(filePath, content);
                this.logger.success(`File created: ${filePath}`);
            }
            else {
                this.logger.info('File creation cancelled');
            }
        }
        catch (error) {
            throw new Error(`Failed to create file: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    extractCodeFromResponse(response) {
        // Try to extract code from markdown code blocks
        const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/;
        const match = response.match(codeBlockRegex);
        if (match && match[1]) {
            return match[1].trim();
        }
        // If no code blocks found, check if the entire response looks like code
        const lines = response.split('\n');
        const codeIndicators = [
            'import ', 'export ', 'function ', 'class ', 'const ', 'let ', 'var ',
            'def ', 'public ', 'private ', 'protected ', '#include', 'package ',
            '<?php', '<!DOCTYPE', '<html', '{', '}', ';'
        ];
        const codeLines = lines.filter(line => codeIndicators.some(indicator => line.trim().startsWith(indicator)) ||
            line.trim().endsWith('{') ||
            line.trim().endsWith(';') ||
            line.trim().endsWith('}'));
        // If more than 30% of lines look like code, treat the whole response as code
        if (codeLines.length / lines.length > 0.3) {
            return response.trim();
        }
        return null;
    }
    createDiff(filePath, oldContent, newContent) {
        const patch = (0, diff_1.createTwoFilesPatch)(filePath, filePath, oldContent, newContent, 'original', 'modified');
        // Colorize the diff
        return patch
            .split('\n')
            .map(line => {
            if (line.startsWith('+') && !line.startsWith('+++')) {
                return chalk_1.default.green(line);
            }
            else if (line.startsWith('-') && !line.startsWith('---')) {
                return chalk_1.default.red(line);
            }
            else if (line.startsWith('@@')) {
                return chalk_1.default.cyan(line);
            }
            return line;
        })
            .join('\n');
    }
}
exports.FileEditor = FileEditor;
//# sourceMappingURL=FileEditor.js.map